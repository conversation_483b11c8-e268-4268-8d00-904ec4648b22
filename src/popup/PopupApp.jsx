import React, { useState, useEffect } from 'react';
import { taskStorage } from '../utils/storage';

const PopupApp = () => {
  const [currentPage, setCurrentPage] = useState({
    isJiraPage: false,
    url: '',
    title: ''
  });
  const [taskStats, setTaskStats] = useState({
    total: 0,
    pending: 0,
    inProgress: 0,
    completed: 0
  });
  const [recentTasks, setRecentTasks] = useState([]);
  const [isLoading, setIsLoading] = useState(true);
  const [quickActions, setQuickActions] = useState([]);

  // 检查当前页面状态
  const checkCurrentPage = async () => {
    try {
      const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });
      setCurrentPage({
        isJiraPage: tab.url.includes('jira.dotfashion.cn'),
        url: tab.url,
        title: tab.title
      });
    } catch (error) {
      console.error('检查页面失败:', error);
    }
  };

  // 加载任务统计
  const loadTaskStats = async () => {
    try {
      const tasks = await taskStorage.getAllTasks();
      const stats = {
        total: tasks.length,
        pending: tasks.filter(t => t.status === 'pending').length,
        inProgress: tasks.filter(t => t.status === 'in-progress').length,
        completed: tasks.filter(t => t.status === 'completed').length
      };
      setTaskStats(stats);

      // 获取最近的3个任务
      const recent = tasks
        .sort((a, b) => new Date(b.updatedAt || b.createdAt) - new Date(a.updatedAt || a.createdAt))
        .slice(0, 3);
      setRecentTasks(recent);
    } catch (error) {
      console.error('加载任务统计失败:', error);
    }
  };

  // 设置快速操作
  const setupQuickActions = () => {
    const actions = [];

    // 如果在JIRA页面，添加数据提取操作
    if (currentPage.isJiraPage) {
      actions.push({
        id: 'extract-jira',
        title: '提取JIRA数据',
        description: '从当前页面提取问题数据',
        icon: '📊',
        action: () => extractJiraData()
      });
    }

    // 添加其他快速操作
    actions.push(
      {
        id: 'open-dashboard',
        title: '打开工作台',
        description: '管理任务和查看详细信息',
        icon: '🏠',
        action: () => openDashboard()
      },
      {
        id: 'open-settings',
        title: '打开设置',
        description: '配置JIRA、GitLab等选项',
        icon: '⚙️',
        action: () => openSettings()
      }
    );

    setQuickActions(actions);
  };
  // 操作函数
  const extractJiraData = async () => {
    try {
      const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });
      await chrome.tabs.sendMessage(tab.id, {
        action: 'extractJiraData',
        type: 'current'
      });
      // 提取后可以选择打开dashboard查看结果
      setTimeout(() => openDashboard(), 1000);
    } catch (error) {
      console.error('提取数据失败:', error);
    }
  };

  const openDashboard = () => {
    chrome.tabs.create({ url: chrome.runtime.getURL('dashboard.html') });
    window.close();
  };

  const openSettings = () => {
    chrome.runtime.openOptionsPage();
    window.close();
  };

  const openJira = () => {
    chrome.tabs.create({ url: 'https://jira.dotfashion.cn' });
    window.close();
  };

  const openGitlab = () => {
    chrome.tabs.create({ url: 'https://gitlab.sheincorp.cn' });
    window.close();
  };

  // 初始化
  useEffect(() => {
    const initialize = async () => {
      setIsLoading(true);
      try {
        await checkCurrentPage();
        await loadTaskStats();
      } catch (error) {
        console.error('初始化失败:', error);
      } finally {
        setIsLoading(false);
      }
    };

    initialize();
  }, []);

  // 当页面状态改变时，更新快速操作
  useEffect(() => {
    setupQuickActions();
  }, [currentPage]);

  if (isLoading) {
    return (
      <div className="popup-container loading">
        <div className="loading-content">
          <div className="loading-spinner"></div>
          <p>加载中...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="popup-container">
      {/* 头部 */}
      <header className="popup-header">
        <div className="logo">
          <span className="logo-icon">🤖</span>
          <h1 className="logo-text">值班萝卜</h1>
        </div>
        <div className="page-status">
          {currentPage.isJiraPage ? (
            <span className="status-badge jira">JIRA页面</span>
          ) : (
            <span className="status-badge normal">普通页面</span>
          )}
        </div>
      </header>

      {/* 任务概览 */}
      <section className="task-overview">
        <h2 className="section-title">任务概览</h2>
        <div className="stats-grid">
          <div className="stat-item">
            <span className="stat-number">{taskStats.total}</span>
            <span className="stat-label">总任务</span>
          </div>
          <div className="stat-item">
            <span className="stat-number">{taskStats.pending}</span>
            <span className="stat-label">待处理</span>
          </div>
          <div className="stat-item">
            <span className="stat-number">{taskStats.inProgress}</span>
            <span className="stat-label">进行中</span>
          </div>
          <div className="stat-item">
            <span className="stat-number">{taskStats.completed}</span>
            <span className="stat-label">已完成</span>
          </div>
        </div>
      </section>

      {/* 最近任务 */}
      {recentTasks.length > 0 && (
        <section className="recent-tasks">
          <h2 className="section-title">最近任务</h2>
          <div className="task-list">
            {recentTasks.map(task => (
              <div key={task.id} className="task-item">
                <div className="task-info">
                  <span className="task-title">{task.title}</span>
                  <span className={`task-status status-${task.status}`}>
                    {task.status === 'pending' ? '待处理' :
                     task.status === 'in-progress' ? '进行中' : '已完成'}
                  </span>
                </div>
              </div>
            ))}
          </div>
        </section>
      )}

      {/* 快速操作 */}
      <section className="quick-actions">
        <h2 className="section-title">快速操作</h2>
        <div className="action-grid">
          {quickActions.map(action => (
            <button
              key={action.id}
              className="action-button"
              onClick={action.action}
            >
              <span className="action-icon">{action.icon}</span>
              <div className="action-content">
                <span className="action-title">{action.title}</span>
                <span className="action-description">{action.description}</span>
              </div>
            </button>
          ))}
        </div>
      </section>

      {/* 常用链接 */}
      <section className="quick-links">
        <h2 className="section-title">常用链接</h2>
        <div className="link-grid">
          <button className="link-button" onClick={openJira}>
            <span className="link-icon">🎫</span>
            <span className="link-text">JIRA</span>
          </button>
          <button className="link-button" onClick={openGitlab}>
            <span className="link-icon">🦊</span>
            <span className="link-text">GitLab</span>
          </button>
        </div>
      </section>

      {/* 底部 */}
      <footer className="popup-footer">
        <button className="footer-button" onClick={openDashboard}>
          <span className="footer-icon">🏠</span>
          <span className="footer-text">打开工作台</span>
        </button>
        <button className="footer-button" onClick={openSettings}>
          <span className="footer-icon">⚙️</span>
          <span className="footer-text">设置</span>
        </button>
      </footer>
    </div>
  );
};

export default PopupApp;