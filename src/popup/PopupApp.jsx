import React, { useState, useEffect, useCallback } from 'react';
import TabNavigation from './components/TabNavigation';
import ExtractTab from './components/ExtractTab';
import ToolsTab from './components/ToolsTab';
import SettingsTab from './components/SettingsTab';
import TaskList from './components/TaskList';
import TaskForm from './components/TaskForm';
import ReleaseGenerator from './components/ReleaseGenerator';
import ConfirmDialog from './components/ConfirmDialog'; // 引入ConfirmDialog
import { taskStorage } from '../utils/storage';

const PopupApp = () => {
  const [activeTab, setActiveTab] = useState('tasks');
  const [jiraStatus, setJiraStatus] = useState({
    isJiraPage: false,
    isFilterPage: false,
    statusText: '检查当前页面...'
  });
  const [extractedData, setExtractedData] = useState([]);
  const [settings, setSettings] = useState({
    region: 'cn',
    gitlabAccessToken: ''
  });
  const [isLoading, setIsLoading] = useState(true); // 新增加载状态
  const [error, setError] = useState(null); // 新增错误状态

  // 任务管理状态
  const [tasks, setTasks] = useState([]);
  const [selectedTask, setSelectedTask] = useState(null);
  const [isFormVisible, setIsFormVisible] = useState(false);
  const [isConfirmOpen, setIsConfirmOpen] = useState(false); // 新增确认对话框状态
  const [taskToDelete, setTaskToDelete] = useState(null); // 新增待删除任务ID状态

  const loadSettings = useCallback(async () => {
    setError(null);
    try {
      const result = await chrome.storage.sync.get([
        'region', 'gitlabAccessToken'
      ]);
      setSettings({
        region: result.region || 'cn',
        gitlabAccessToken: result.gitlabAccessToken || ''
      });
    } catch (error) {
      console.error('加载设置失败:', error);
      setError('加载设置失败: ' + error.message);
      throw error; // 抛出错误以便initializeApp捕获
    }
  }, []);

  const checkCurrentPage = useCallback(async () => {
    setError(null);
    try {
      const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });
      const url = tab.url;
      
      if (url.includes('jira.dotfashion.cn')) {
        const isFilterPage = url.includes('/issues/?filter=');
        setJiraStatus({
          isJiraPage: true,
          isFilterPage,
          statusText: isFilterPage 
            ? '检测到JIRA筛选器页面，可以提取列表数据'
            : '当前在JIRA页面，可以提取数据'
        });
      } else {
        setJiraStatus({
          isJiraPage: false,
          isFilterPage: false,
          statusText: '请导航到JIRA页面以提取数据'
        });
      }
    } catch (error) {
      console.error('检查页面失败:', error);
      setError('检查页面失败: ' + error.message);
      throw error; // 抛出错误以便initializeApp捕获
    }
  }, []);

  const handleExtractData = useCallback(async (type) => {
    setIsLoading(true);
    setError(null);
    try {
      const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });
      
      // 发送消息到content script
      const response = await chrome.tabs.sendMessage(tab.id, {
        action: 'extractJiraData',
        type: type
      });
      
      if (response && response.success) {
        setExtractedData(response.data);
        setActiveTab('extract'); // 确保在提取标签页显示结果
      } else {
        const errorMessage = response?.error || '未知错误';
        console.error('提取数据失败:', errorMessage);
        setError('提取数据失败: ' + errorMessage);
      }
    } catch (error) {
      console.error('提取数据时出错:', error);
      setError('提取数据时出错: ' + error.message);
    } finally {
      setIsLoading(false);
    }
  }, []);

  const saveSettings = useCallback(async (newSettings) => {
    setIsLoading(true);
    setError(null);
    try {
      await chrome.storage.sync.set(newSettings);
      setSettings(prev => ({ ...prev, ...newSettings }));
    } catch (error) {
      console.error('保存设置失败:', error);
      setError('保存设置失败: ' + error.message);
    } finally {
      setIsLoading(false);
    }
  }, []);
  
  // 加载任务列表
  const loadTasks = useCallback(async () => {
    setError(null);
    try {
      const taskList = await taskStorage.getAllTasks();
      setTasks(taskList);
    } catch (error) {
      console.error('加载任务失败:', error);
      setError('加载任务失败: ' + error.message);
      throw error; // 抛出错误以便initializeApp捕获
    }
  }, []);
  
  // 创建新任务
  const handleCreateTask = useCallback(async (taskData) => {
    setIsLoading(true);
    setError(null);
    try {
      const newTask = await taskStorage.createTask(taskData);
      if (newTask) {
        setTasks(prev => [...prev, newTask]);
        setIsFormVisible(false);
      }
    } catch (error) {
      console.error('创建任务失败:', error);
      setError('创建任务失败: ' + error.message);
    } finally {
      setIsLoading(false);
    }
  }, []);
  
  // 更新任务
  const handleUpdateTask = useCallback(async (taskId, taskData) => {
    setIsLoading(true);
    setError(null);
    try {
      const updatedTask = await taskStorage.updateTask(taskId, taskData);
      if (updatedTask) {
        setTasks(prev => prev.map(task => 
          task.id === taskId ? updatedTask : task
        ));
        setSelectedTask(null);
        setIsFormVisible(false);
      }
    } catch (error) {
      console.error('更新任务失败:', error);
      setError('更新任务失败: ' + error.message);
    } finally {
      setIsLoading(false);
    }
  }, []);
  
  // 删除任务
  const handleDeleteTask = useCallback(async (taskId) => {
    setTaskToDelete(taskId); // 设置待删除任务ID
    setIsConfirmOpen(true); // 打开确认对话框
  }, []);

  // 确认删除任务
  const confirmDeleteTask = useCallback(async () => {
    setIsConfirmOpen(false); // 关闭确认对话框
    if (!taskToDelete) return; // 如果没有待删除任务，则返回

    setIsLoading(true);
    setError(null);
    try {
      const success = await taskStorage.deleteTask(taskToDelete);
      if (success) {
        setTasks(prev => prev.filter(task => task.id !== taskToDelete));
      }
    } catch (error) {
      console.error('删除任务失败:', error);
      setError('删除任务失败: ' + error.message);
    } finally {
      setIsLoading(false);
      setTaskToDelete(null); // 清除待删除任务ID
    }
  }, [taskToDelete]);

  // 取消删除任务
  const cancelDeleteTask = useCallback(() => {
    setIsConfirmOpen(false); // 关闭确认对话框
    setTaskToDelete(null); // 清除待删除任务ID
  }, []);

  // 处理任务保存
  const handleSaveTask = useCallback((taskData) => {
    if (selectedTask) {
      handleUpdateTask(selectedTask.id, taskData);
    } else {
      handleCreateTask(taskData);
    }
  }, [selectedTask, handleUpdateTask, handleCreateTask]);

  // 处理任务选择
  const handleTaskSelect = useCallback((task) => {
    setSelectedTask(task);
    setIsFormVisible(true);
  }, []);

  // 处理表单取消
  const handleFormCancel = useCallback(() => {
    setSelectedTask(null);
    setIsFormVisible(false);
  }, []);

  useEffect(() => {
    // 初始化：加载设置、任务和检查当前页面
    const initializeApp = async () => {
      setIsLoading(true);
      setError(null);
      try {
        await loadSettings();
        await loadTasks();
        await checkCurrentPage();
      } catch (err) {
        setError('初始化失败: ' + err.message);
      } finally {
        setIsLoading(false);
      }
    };
    initializeApp();
  }, [loadSettings, loadTasks, checkCurrentPage]);

  // 添加键盘快捷键支持
  useEffect(() => {
    const handleKeyDown = (event) => {
      if (event.altKey) {
        switch (event.key) {
          case '1':
            setActiveTab('tasks');
            break;
          case '2':
            setActiveTab('extract');
            break;
          case '3':
            setActiveTab('tools');
            break;
          case '4':
            setActiveTab('settings');
            break;
          default:
            break;
        }
      }
    };

    window.addEventListener('keydown', handleKeyDown);

    return () => {
      window.removeEventListener('keydown', handleKeyDown);
    };
  }, []); // 空依赖数组表示只在组件挂载和卸载时运行

  const renderTabContent = () => {
    if (isLoading) {
      return <div className="loading-indicator">加载中...</div>;
    }
    if (error) {
      return <div className="error-message">错误: {error}</div>;
    }
    switch (activeTab) {
      case 'tasks':
        return (
          <div className="tab-content active">
            <div className="section">
              <div className="section-header">
                <h3>任务管理</h3>
                {!isFormVisible && (
                  <button 
                    className="btn btn-primary btn-sm" 
                    onClick={() => setIsFormVisible(true)}
                  >
                    {selectedTask ? '编辑任务' : '创建新任务'}
                  </button>
                )}
              </div>
              {isFormVisible ? (
                <TaskForm 
                  task={selectedTask}
                  onSave={handleSaveTask}
                  onCancel={handleFormCancel}
                />
              ) : (
                <TaskList 
                  tasks={tasks}
                  onTaskUpdate={handleUpdateTask}
                  onTaskDelete={handleDeleteTask}
                  onTaskSelect={handleTaskSelect}
                />
              )}
            </div>
          </div>
        );
      case 'extract':
        return <ExtractTab jiraStatus={jiraStatus} onExtractData={handleExtractData} extractedData={extractedData} />;
      case 'tools':
        return <ToolsTab />;
      case 'release':
        return <ReleaseGenerator />;
      case 'settings':
        return <SettingsTab settings={settings} onSaveSettings={saveSettings} />;
      default:
        return null;
    }
  };

  return (
     <div className="popup-container">
      <TabNavigation activeTab={activeTab} onTabChange={setActiveTab} />
      <div className="tab-content-wrapper">
        {renderTabContent()}
      </div>
      <ConfirmDialog
        isOpen={isConfirmOpen}
        title="确认删除"
        message="您确定要删除此任务吗？此操作不可撤销。"
        onConfirm={confirmDeleteTask}
        onCancel={cancelDeleteTask}
      />
    </div>
  );
};

export default PopupApp;