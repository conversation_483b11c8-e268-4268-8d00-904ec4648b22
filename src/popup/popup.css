/* DutyRobot Extension Popup Styles - Redesigned */

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  font-size: 14px;
  line-height: 1.5;
  color: #333;
  background: #f8f9fa;
  width: 380px;
  min-height: 600px;
}

/* 容器样式 */
.popup-container {
  width: 100%;
  min-height: 600px;
  background: white;
  display: flex;
  flex-direction: column;
}

.popup-container.loading {
  justify-content: center;
  align-items: center;
}

/* 加载状态 */
.loading-content {
  text-align: center;
  padding: 40px 20px;
}

.loading-spinner {
  width: 32px;
  height: 32px;
  border: 3px solid #f3f3f3;
  border-top: 3px solid #667eea;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto 16px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 头部样式 */
.popup-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.logo {
  display: flex;
  align-items: center;
  gap: 8px;
}

.logo-icon {
  font-size: 24px;
}

.logo-text {
  font-size: 18px;
  font-weight: 600;
  margin: 0;
}

.page-status {
  display: flex;
  align-items: center;
}

.status-badge {
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 11px;
  font-weight: 500;
  background: rgba(255, 255, 255, 0.2);
  color: white;
}

.status-badge.jira {
  background: rgba(76, 175, 80, 0.3);
}

.status-badge.normal {
  background: rgba(255, 255, 255, 0.2);
}

/* 内容区域 */
section {
  padding: 20px;
  border-bottom: 1px solid #f0f0f0;
}

section:last-child {
  border-bottom: none;
}

.section-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin-bottom: 16px;
  display: flex;
  align-items: center;
  gap: 8px;
}

/* 任务概览 */
.task-overview {
  background: #f8f9fa;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 12px;
}

.stat-item {
  text-align: center;
  padding: 12px 8px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.stat-number {
  display: block;
  font-size: 20px;
  font-weight: 700;
  color: #667eea;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 11px;
  color: #6c757d;
  font-weight: 500;
}

/* 最近任务 */
.recent-tasks {
  background: white;
}

.task-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.task-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px;
  background: #f8f9fa;
  border-radius: 6px;
  border-left: 3px solid #667eea;
}

.task-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.task-title {
  font-size: 13px;
  font-weight: 500;
  color: #333;
  line-height: 1.3;
}

.task-status {
  font-size: 11px;
  padding: 2px 6px;
  border-radius: 4px;
  font-weight: 500;
  width: fit-content;
}

.status-pending {
  background: #fff3cd;
  color: #856404;
}

.status-in-progress {
  background: #d1ecf1;
  color: #0c5460;
}

.status-completed {
  background: #d4edda;
  color: #155724;
}

/* 快速操作 */
.quick-actions {
  background: white;
}

.action-grid {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.action-button {
  display: flex;
  align-items: center;
  padding: 16px;
  background: white;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
  text-align: left;
  width: 100%;
}

.action-button:hover {
  border-color: #667eea;
  box-shadow: 0 2px 8px rgba(102, 126, 234, 0.1);
  transform: translateY(-1px);
}

.action-icon {
  font-size: 24px;
  margin-right: 16px;
  flex-shrink: 0;
}

.action-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.action-title {
  font-size: 14px;
  font-weight: 600;
  color: #333;
}

.action-description {
  font-size: 12px;
  color: #6c757d;
  line-height: 1.3;
}

/* 常用链接 */
.quick-links {
  background: white;
}

.link-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 12px;
}

.link-button {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 16px 12px;
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
  text-decoration: none;
  color: inherit;
}

.link-button:hover {
  background: #e9ecef;
  border-color: #667eea;
  transform: translateY(-1px);
}

.link-icon {
  font-size: 24px;
  margin-bottom: 8px;
}

.link-text {
  font-size: 12px;
  font-weight: 500;
  color: #333;
}

/* 底部 */
.popup-footer {
  background: #f8f9fa;
  padding: 16px 20px;
  display: flex;
  gap: 12px;
  margin-top: auto;
  border-top: 1px solid #e9ecef;
}

.footer-button {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 12px 8px;
  background: white;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
  text-decoration: none;
  color: inherit;
}

.footer-button:hover {
  background: #667eea;
  color: white;
  border-color: #667eea;
  transform: translateY(-1px);
}

.footer-icon {
  font-size: 20px;
  margin-bottom: 4px;
}

.footer-text {
  font-size: 11px;
  font-weight: 500;
}

/* 响应式设计 */
@media (max-width: 400px) {
  body {
    width: 320px;
  }

  .stats-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 8px;
  }

  .link-grid {
    grid-template-columns: 1fr;
  }

  .popup-footer {
    flex-direction: column;
    gap: 8px;
  }

  .footer-button {
    flex-direction: row;
    justify-content: center;
    gap: 8px;
  }

  .footer-icon {
    margin-bottom: 0;
  }
}


