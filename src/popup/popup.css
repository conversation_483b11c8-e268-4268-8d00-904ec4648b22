/* DutyRobot Extension Popup Styles */

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  font-size: 14px;
  line-height: 1.5;
  color: #333;
  background: #f8f9fa;
}

.container {
  width: 400px;
  min-height: 500px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

/* Header */
.header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 20px;
  text-align: center;
}

.header h1 {
  font-size: 20px;
  font-weight: 600;
  margin-bottom: 4px;
}

.subtitle {
  font-size: 12px;
  opacity: 0.9;
}

/* Navigation Tabs */
.nav-tabs {
  display: flex;
  background: #f8f9fa;
  border-bottom: 1px solid #e9ecef;
}

.tab-btn {
  flex: 1;
  padding: 12px 16px;
  border: none;
  background: transparent;
  color: #6c757d;
  font-size: 13px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  border-bottom: 2px solid transparent;
}

.tab-btn:hover {
  background: #e9ecef;
  color: #495057;
}

.tab-btn.active {
  color: #667eea;
  border-bottom-color: #667eea;
  background: white;
}

/* Tab Content */
.tab-content-container {
  padding: 20px;
}

.tab-content {
  display: none;
}

.tab-content.active {
  display: block;
}

/* Sections */
.section {
  margin-bottom: 24px;
}

.section h3 {
  font-size: 16px;
  font-weight: 600;
  color: #495057;
  margin-bottom: 16px;
  padding-bottom: 8px;
  border-bottom: 1px solid #e9ecef;
}

.section h4 {
  font-size: 14px;
  font-weight: 600;
  color: #6c757d;
  margin-bottom: 12px;
}

/* Status Info */
.status-info {
  display: flex;
  align-items: center;
  padding: 12px;
  background: #f8f9fa;
  border-radius: 6px;
  margin-bottom: 16px;
}

.status-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: #28a745;
  margin-right: 8px;
  flex-shrink: 0;
}

.status-dot.warning {
  background: #ffc107;
}

.status-dot.error {
  background: #dc3545;
}

.status-text {
  font-size: 13px;
  color: #6c757d;
}

/* Buttons */
.btn {
  display: inline-flex;
  align-items: center;
  padding: 8px 16px;
  border: 1px solid transparent;
  border-radius: 6px;
  font-size: 13px;
  font-weight: 500;
  text-decoration: none;
  cursor: pointer;
  transition: all 0.2s ease;
  background: white;
  color: #495057;
}

.btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

.btn-primary {
  background: #667eea;
  color: white;
  border-color: #667eea;
}

.btn-primary:hover:not(:disabled) {
  background: #5a6fd8;
}

.btn-secondary {
  background: #6c757d;
  color: white;
  border-color: #6c757d;
}

.btn-secondary:hover:not(:disabled) {
  background: #5a6268;
}

.btn-small {
  padding: 6px 12px;
  font-size: 12px;
}

.btn-icon {
  margin-right: 6px;
  font-size: 14px;
}

/* Action Buttons */

/* Confirm Dialog */
.confirm-dialog-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.confirm-dialog {
  background-color: white;
  padding: 25px;
  border-radius: 8px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
  width: 300px;
  text-align: center;
}

.confirm-dialog-title {
  font-size: 18px;
  font-weight: 600;
  color: #333;
  margin-bottom: 15px;
}

.confirm-dialog-message {
  font-size: 14px;
  color: #555;
  margin-bottom: 25px;
}

.confirm-dialog-actions {
  display: flex;
  justify-content: center;
  gap: 15px;
}

.confirm-dialog-actions .btn {
  min-width: 80px;
}
.action-buttons {
  display: flex;
  gap: 8px;
  margin-bottom: 16px;
}

.action-buttons .btn {
  flex: 1;
}

/* Extracted Data */
.extracted-data {
  background: #f8f9fa;
  border-radius: 6px;
  padding: 16px;
  margin-bottom: 16px;
}

.data-count {
  font-size: 12px;
  color: #6c757d;
  font-weight: normal;
}

.data-preview {
  margin: 12px 0;
  max-height: 120px;
  overflow-y: auto;
}

.data-item {
  padding: 6px 0;
  border-bottom: 1px solid #e9ecef;
  font-size: 12px;
  line-height: 1.4;
}

.data-item:last-child {
  border-bottom: none;
}

.export-options {
  display: flex;
  gap: 6px;
  margin-top: 12px;
}

/* Quick Actions */
.quick-actions {
  margin-top: 20px;
}

.action-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 8px;
}

.action-card {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 12px;
  background: white;
  border: 1px solid #e9ecef;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s ease;
  text-decoration: none;
  color: inherit;
}

.action-card:hover {
  border-color: #667eea;
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.action-icon {
  font-size: 20px;
  margin-bottom: 6px;
}

.action-text {
  font-size: 12px;
  font-weight: 500;
  text-align: center;
}

.action-desc {
  font-size: 11px;
  color: #6c757d;
  text-align: center;
  margin-top: 4px;
}

/* Project Grid */
.project-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 8px;
  margin-bottom: 16px;
}

.project-card {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px;
  background: white;
  border: 1px solid #e9ecef;
  border-radius: 6px;
}

.project-info strong {
  display: block;
  font-size: 13px;
  color: #495057;
}

.project-id {
  font-size: 11px;
  color: #6c757d;
}

.project-actions {
  display: flex;
  gap: 6px;
}

/* Link Grid */
.link-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 6px;
}

.link-card {
  display: flex;
  align-items: center;
  padding: 10px 12px;
  background: white;
  border: 1px solid #e9ecef;
  border-radius: 6px;
  text-decoration: none;
  color: inherit;
  transition: all 0.2s ease;
}

.link-card:hover {
  border-color: #667eea;
  background: #f8f9fa;
}

.link-icon {
  margin-right: 8px;
  font-size: 16px;
}

.link-text {
  font-size: 13px;
  font-weight: 500;
}

/* Form Elements */
.form-group {
  margin-bottom: 16px;
}

.form-group label {
  display: block;
  font-size: 13px;
  font-weight: 500;
  color: #495057;
  margin-bottom: 6px;
}

.form-control {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid #ced4da;
  border-radius: 4px;
  font-size: 13px;
  transition: border-color 0.2s ease;
}

.form-control:focus {
  outline: none;
  border-color: #667eea;
  box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.1);
}

.input-group {
  display: flex;
}

.input-group .form-control {
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
  border-right: none;
}

.input-group .btn {
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
}

.form-text {
  font-size: 11px;
  color: #6c757d;
  margin-top: 4px;
}

.form-text a {
  color: #667eea;
  text-decoration: none;
  margin-left: 8px;
}

.form-text a:hover {
  text-decoration: underline;
}

.checkbox-group {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 8px;
}

.checkbox-label {
  display: flex;
  align-items: center;
  font-size: 12px;
  cursor: pointer;
}

.checkbox-label input[type="checkbox"] {
  margin-right: 6px;
}

.form-actions {
  display: flex;
  gap: 8px;
  margin-top: 16px;
}

/* Connection Status */
.connection-status {
  padding: 8px 12px;
  border-radius: 4px;
  font-size: 12px;
  margin-top: 12px;
}

.connection-status.success {
  background: #d4edda;
  color: #155724;
  border: 1px solid #c3e6cb;
}

.connection-status.error {
  background: #f8d7da;
  color: #721c24;
  border: 1px solid #f5c6cb;
}

.connection-status.info {
  background: #d1ecf1;
  color: #0c5460;
  border: 1px solid #bee5eb;
}

/* Tool Section */
.tool-section {
  margin-bottom: 20px;
}

.data-preview pre {
  background: #f8f9fa;
  padding: 12px;
  border-radius: 4px;
  font-size: 11px;
  max-height: 200px;
  overflow: auto;
  white-space: pre-wrap;
  word-wrap: break-word;
}

/* Task Management Styles */
.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.task-list-container {
  margin-bottom: 20px;
}

.task-list-header {
  display: flex;
  flex-direction: column;
  gap: 10px;
  margin-bottom: 16px;
}

.search-filter {
  margin-bottom: 8px;
}

.status-filter {
  display: flex;
  gap: 6px;
  overflow-x: auto;
  padding-bottom: 4px;
}

.filter-btn {
  padding: 6px 12px;
  border: 1px solid #e9ecef;
  border-radius: 20px;
  background: white;
  font-size: 12px;
  cursor: pointer;
  white-space: nowrap;
  transition: all 0.2s ease;
}

.filter-btn:hover {
  background: #f8f9fa;
}

.filter-btn.active {
  background: #667eea;
  color: white;
  border-color: #667eea;
}

.task-count {
  font-size: 12px;
  color: #6c757d;
  margin-bottom: 12px;
}

.empty-state {
  text-align: center;
  padding: 30px 0;
  color: #6c757d;
}

.task-items {
  display: flex;
  flex-direction: column;
  gap: 12px;
  max-height: 400px;
  overflow-y: auto;
}

.task-item {
  background: white;
  border: 1px solid #e9ecef;
  border-radius: 6px;
  padding: 12px;
  transition: all 0.2s ease;
}

.task-item:hover {
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  transform: translateY(-1px);
}

.task-item.status-todo {
  border-left: 3px solid #ffc107;
}

.task-item.status-in_progress {
  border-left: 3px solid #17a2b8;
}

.task-item.status-done {
  border-left: 3px solid #28a745;
}

.task-item.status-cancelled {
  border-left: 3px solid #6c757d;
  opacity: 0.8;
}

.task-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 8px;
}

.task-title {
  font-weight: 600;
  font-size: 14px;
  color: #495057;
  cursor: pointer;
}

.task-title:hover {
  color: #667eea;
}

.task-actions {
  display: flex;
  gap: 4px;
}

.btn-icon {
  background: none;
  border: none;
  font-size: 14px;
  cursor: pointer;
  color: #6c757d;
  padding: 2px;
}

.btn-icon:hover {
  color: #dc3545;
}

.task-meta {
  display: flex;
  gap: 6px;
  margin-bottom: 8px;
}

.task-jira {
  background: #f8f9fa;
  color: #495057;
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 11px;
  font-weight: 500;
}

.task-priority, .task-status {
  padding: 2px 6px;
  border-radius: 4px;
  color: white;
  font-size: 11px;
  font-weight: 500;
}

.task-description {
  font-size: 12px;
  color: #6c757d;
  margin-bottom: 12px;
  line-height: 1.4;
}

.task-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 11px;
}

.task-dates {
  color: #6c757d;
  display: flex;
  gap: 8px;
}

.task-status-actions {
  display: flex;
  gap: 4px;
}

.status-btn {
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 11px;
  border: none;
  cursor: pointer;
  transition: all 0.2s ease;
}

.status-btn.todo {
  background: #fff3cd;
  color: #856404;
}

.status-btn.in-progress {
  background: #d1ecf1;
  color: #0c5460;
}

.status-btn.done {
  background: #d4edda;
  color: #155724;
}

.status-btn.cancelled {
  background: #e2e3e5;
  color: #383d41;
}

.status-btn:hover {
  filter: brightness(0.95);
}

/* Task Form Styles */
.task-form-container {
  background: white;
  border-radius: 6px;
  padding: 16px;
  margin-bottom: 16px;
}

.form-row {
  display: flex;
  gap: 12px;
  margin-bottom: 16px;
}

.form-group-half {
  flex: 1;
}

.required {
  color: #dc3545;
}

.invalid-feedback {
  color: #dc3545;
  font-size: 11px;
  margin-top: 4px;
}

.tag-input-container {
  display: flex;
  gap: 8px;
  margin-bottom: 8px;
}

.tags-container {
  display: flex;
  flex-wrap: wrap;
  gap: 6px;
}

.tag {
  background: #e9ecef;
  color: #495057;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  display: flex;
  align-items: center;
}

.tag-remove {
  background: none;
  border: none;
  color: #6c757d;
  margin-left: 4px;
  cursor: pointer;
  font-size: 14px;
  line-height: 1;
  padding: 0 2px;
}

.tag-remove:hover {
  color: #dc3545;
}

/* Loading and Error Messages */
.loading-indicator,
.error-message {
  padding: 20px;
  text-align: center;
  font-size: 14px;
  font-weight: 500;
  border-radius: 8px;
  margin: 20px;
}

.loading-indicator {
  background-color: #e0f7fa; /* Light blue */
  color: #00796b; /* Dark teal */
  border: 1px solid #b2ebf2;
}

.error-message {
  background-color: #ffe0b2; /* Light orange */
  color: #e65100; /* Dark orange */
  border: 1px solid #ffcc80;
}