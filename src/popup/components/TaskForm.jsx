import React, { useState, useEffect } from 'react';

/**
 * 任务创建和编辑表单组件
 */
const TaskForm = ({ task, onSave, onCancel }) => {
  // 初始化表单数据
  const [formData, setFormData] = useState({
    title: '',
    description: '',
    status: 'todo',
    priority: 'medium',
    jiraKey: '',
    gitlabMR: '',
    dueDate: '',
    tags: []
  });
  
  // 表单验证状态
  const [errors, setErrors] = useState({});
  // 标签输入
  const [tagInput, setTagInput] = useState('');

  // 如果传入了任务数据，则填充表单
  useEffect(() => {
    if (task) {
      setFormData({
        title: task.title || '',
        description: task.description || '',
        status: task.status || 'todo',
        priority: task.priority || 'medium',
        jiraKey: task.jiraKey || '',
        gitlabMR: task.gitlabMR || '',
        dueDate: task.dueDate ? new Date(task.dueDate).toISOString().split('T')[0] : '',
        tags: Array.isArray(task.tags) ? [...task.tags] : []
      });
    }
  }, [task]);

  // 处理输入变化
  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
    
    // 清除对应字段的错误
    if (errors[name]) {
      setErrors(prev => ({
        ...prev,
        [name]: ''
      }));
    }
  };

  // 添加标签
  const handleAddTag = () => {
    if (tagInput.trim() && !formData.tags.includes(tagInput.trim())) {
      setFormData(prev => ({
        ...prev,
        tags: [...prev.tags, tagInput.trim()]
      }));
      setTagInput('');
    }
  };

  // 删除标签
  const handleRemoveTag = (tagToRemove) => {
    setFormData(prev => ({
      ...prev,
      tags: prev.tags.filter(tag => tag !== tagToRemove)
    }));
  };

  // 表单提交
  const handleSubmit = (e) => {
    e.preventDefault();
    
    // 表单验证
    const newErrors = {};
    if (!formData.title.trim()) {
      newErrors.title = '请输入任务标题';
    }
    
    if (Object.keys(newErrors).length > 0) {
      setErrors(newErrors);
      return;
    }
    
    // 准备保存的数据
    const taskData = {
      ...formData,
      dueDate: formData.dueDate ? new Date(formData.dueDate).toISOString() : null
    };
    
    onSave(taskData);
  };

  return (
    <div className="task-form-container">
      <form onSubmit={handleSubmit}>
        <div className="form-group">
          <label htmlFor="title">任务标题 <span className="required">*</span></label>
          <input
            type="text"
            id="title"
            name="title"
            className={`form-control ${errors.title ? 'is-invalid' : ''}`}
            value={formData.title}
            onChange={handleInputChange}
            placeholder="输入任务标题"
          />
          {errors.title && <div className="invalid-feedback">{errors.title}</div>}
        </div>

        <div className="form-group">
          <label htmlFor="description">任务描述</label>
          <textarea
            id="description"
            name="description"
            className="form-control"
            value={formData.description}
            onChange={handleInputChange}
            placeholder="输入任务描述"
            rows="3"
          />
        </div>

        <div className="form-row">
          <div className="form-group form-group-half">
            <label htmlFor="status">状态</label>
            <select
              id="status"
              name="status"
              className="form-control"
              value={formData.status}
              onChange={handleInputChange}
            >
              <option value="todo">待办</option>
              <option value="in_progress">进行中</option>
              <option value="done">已完成</option>
              <option value="cancelled">已取消</option>
            </select>
          </div>

          <div className="form-group form-group-half">
            <label htmlFor="priority">优先级</label>
            <select
              id="priority"
              name="priority"
              className="form-control"
              value={formData.priority}
              onChange={handleInputChange}
            >
              <option value="low">低</option>
              <option value="medium">中</option>
              <option value="high">高</option>
            </select>
          </div>
        </div>

        <div className="form-row">
          <div className="form-group form-group-half">
            <label htmlFor="jiraKey">JIRA编号</label>
            <input
              type="text"
              id="jiraKey"
              name="jiraKey"
              className="form-control"
              value={formData.jiraKey}
              onChange={handleInputChange}
              placeholder="例如: LPMPM-1234"
            />
          </div>

          <div className="form-group form-group-half">
            <label htmlFor="gitlabMR">GitLab MR</label>
            <input
              type="text"
              id="gitlabMR"
              name="gitlabMR"
              className="form-control"
              value={formData.gitlabMR}
              onChange={handleInputChange}
              placeholder="例如: !123"
            />
          </div>
        </div>

        <div className="form-group">
          <label htmlFor="dueDate">截止日期</label>
          <input
            type="date"
            id="dueDate"
            name="dueDate"
            className="form-control"
            value={formData.dueDate}
            onChange={handleInputChange}
          />
        </div>

        <div className="form-group">
          <label>标签</label>
          <div className="tag-input-container">
            <input
              type="text"
              className="form-control"
              value={tagInput}
              onChange={(e) => setTagInput(e.target.value)}
              placeholder="添加标签"
              onKeyPress={(e) => e.key === 'Enter' && (e.preventDefault(), handleAddTag())}
            />
            <button 
              type="button" 
              className="btn btn-secondary btn-sm"
              onClick={handleAddTag}
            >
              添加
            </button>
          </div>
          
          <div className="tags-container">
            {formData.tags.map((tag, index) => (
              <span key={index} className="tag">
                {tag}
                <button 
                  type="button" 
                  className="tag-remove" 
                  onClick={() => handleRemoveTag(tag)}
                >
                  ×
                </button>
              </span>
            ))}
          </div>
        </div>

        <div className="form-actions">
          <button type="button" className="btn btn-secondary" onClick={onCancel}>
            取消
          </button>
          <button type="submit" className="btn btn-primary">
            {task ? '更新任务' : '创建任务'}
          </button>
        </div>
      </form>
    </div>
  );
};

export default TaskForm;