import React, { useState, useEffect } from 'react';
import { getStorage, setStorage } from '../../utils/storage';
import { showNotification } from '../../utils/notifications';

const ReleaseGenerator = () => {
  const [jira<PERSON><PERSON>, setJ<PERSON><PERSON>ey] = useState('');
  const [mrLink, setMrLink] = useState('');
  const [assignee, setAssignee] = useState('');
  const [presetAssignees, setPresetAssignees] = useState([]);
  const [releaseInfo, setReleaseInfo] = useState('');

  useEffect(() => {
    // 加载预设人员列表
    const loadPresetAssignees = async () => {
      const storedAssignees = await getStorage('presetAssignees');
      if (storedAssignees) {
        setPresetAssignees(storedAssignees);
      }
    };
    loadPresetAssignees();
  }, []);

  useEffect(() => {
    generateReleaseInfo();
  }, [jira<PERSON><PERSON>, mr<PERSON>ink, assignee]);

  const generateReleaseInfo = () => {
    let info = `【发版信息】\n`;
    if (jiraKey) {
      info += `需求号: ${jiraKey}\n`;
    }
    if (mrLink) {
      info += `MR链接: ${mrLink}\n`;
    }
    if (assignee) {
      info += `负责人: ${assignee}\n`;
    }
    info += `\n请相关同学及时关注。`;
    setReleaseInfo(info);
  };

  const handleCopy = async () => {
    try {
      await navigator.clipboard.writeText(releaseInfo);
      showNotification('复制成功', '发版信息已复制到剪贴板！');
    } catch (err) {
      console.error('Failed to copy: ', err);
      showNotification('复制失败', '无法复制发版信息，请手动复制。', 'error');
    }
  };

  const handleAddAssignee = async () => {
    if (assignee && !presetAssignees.includes(assignee)) {
      const updatedAssignees = [...presetAssignees, assignee];
      setPresetAssignees(updatedAssignees);
      await setStorage('presetAssignees', updatedAssignees);
      showNotification('添加成功', `已将 ${assignee} 添加到预设人员列表。`);
    }
  };

  const handleRemoveAssignee = async (assigneeToRemove) => {
    const updatedAssignees = presetAssignees.filter(a => a !== assigneeToRemove);
    setPresetAssignees(updatedAssignees);
    await setStorage('presetAssignees', updatedAssignees);
    showNotification('删除成功', `已将 ${assigneeToRemove} 从预设人员列表移除。`);
  };

  return (
    <div className="release-generator-container">
      <h3>临时发版工具</h3>
      <div className="form-group">
        <label htmlFor="jiraKey">需求号:</label>
        <input
          type="text"
          id="jiraKey"
          className="form-control"
          value={jiraKey}
          onChange={(e) => setJiraKey(e.target.value)}
          placeholder="例如: LPMPM-1234"
        />
      </div>

      <div className="form-group">
        <label htmlFor="mrLink">MR链接:</label>
        <input
          type="text"
          id="mrLink"
          className="form-control"
          value={mrLink}
          onChange={(e) => setMrLink(e.target.value)}
          placeholder="例如: https://gitlab.com/project/-/merge_requests/123"
        />
      </div>

      <div className="form-group">
        <label htmlFor="assignee">负责人:</label>
        <div className="input-group">
          <input
            type="text"
            id="assignee"
            className="form-control"
            value={assignee}
            onChange={(e) => setAssignee(e.target.value)}
            placeholder="例如: @张三"
          />
          <button className="btn btn-primary" onClick={handleAddAssignee}>添加预设</button>
        </div>
      </div>

      {presetAssignees.length > 0 && (
        <div className="form-group">
          <label>预设人员:</label>
          <div className="preset-assignees-tags">
            {presetAssignees.map((pa, index) => (
              <span key={index} className="tag">
                {pa}
                <button className="tag-remove-btn" onClick={() => setAssignee(pa)}>选择</button>
                <button className="tag-remove-btn" onClick={() => handleRemoveAssignee(pa)}>x</button>
              </span>
            ))}
          </div>
        </div>
      )}

      <div className="form-group">
        <label>发版信息预览:</label>
        <textarea
          className="form-control"
          rows="6"
          value={releaseInfo}
          readOnly
        ></textarea>
      </div>

      <button className="btn btn-success" onClick={handleCopy}>一键复制</button>
    </div>
  );
};

export default ReleaseGenerator;