import React, { useState } from 'react';

const ToolsTab = ({ settings }) => {
  const [gitlabData, setGitlabData] = useState(null);
  const [loading, setLoading] = useState(false);

  const fetchGitlabData = async (projectId, endpoint) => {
    setLoading(true);
    try {
      const response = await chrome.runtime.sendMessage({
        action: 'fetchGitlabData',
        projectId,
        endpoint
      });
      
      if (response.success) {
        setGitlabData(response.data);
      } else {
        console.error('获取GitLab数据失败:', response.error);
      }
    } catch (error) {
      console.error('请求GitLab数据时出错:', error);
    } finally {
      setLoading(false);
    }
  };

  const openGitlabProject = (projectId) => {
    const url = `https://gitlab.sheincorp.cn/projects/${projectId}`;
    chrome.tabs.create({ url });
  };

  const projectConfigs = {
    cn: {
      "wms": 424,
      "mot": 511,
      "wsk": 5730,
      "wacs": 10334,
      "wop": 19118,
      "ips": 2456,
      "ips-w": 2457,
      "lpmpm": 10100
    }
  };

  const currentProjects = projectConfigs[settings.region] || projectConfigs.cn;

  return (
    <div id="tools-tab">
      <div className="section">
        <h3>GitLab工具</h3>
        
        <div className="tool-section">
          <h4>项目快速访问</h4>
          <div className="project-grid">
            {Object.entries(currentProjects).map(([name, id]) => (
              <div key={name} className="project-card">
                <div className="project-info">
                  <strong>{name.toUpperCase()}</strong>
                  <span className="project-id">ID: {id}</span>
                </div>
                <div className="project-actions">
                  <button 
                    className="btn btn-small"
                    onClick={() => openGitlabProject(id)}
                  >
                    打开项目
                  </button>
                  <button 
                    className="btn btn-small btn-secondary"
                    onClick={() => fetchGitlabData(id, 'issues')}
                    disabled={loading}
                  >
                    获取Issues
                  </button>
                </div>
              </div>
            ))}
          </div>
        </div>

        <div className="tool-section">
          <h4>数据分析工具</h4>
          <div className="action-grid">
            <button className="action-card">
              <span className="action-icon">📈</span>
              <span className="action-text">生成报告</span>
              <span className="action-desc">基于提取的数据生成分析报告</span>
            </button>
            <button className="action-card">
              <span className="action-icon">🔄</span>
              <span className="action-text">数据同步</span>
              <span className="action-desc">同步JIRA和GitLab数据</span>
            </button>
            <button className="action-card">
              <span className="action-icon">📋</span>
              <span className="action-text">模板管理</span>
              <span className="action-desc">管理数据导出模板</span>
            </button>
          </div>
        </div>

        {gitlabData && (
          <div className="tool-section">
            <h4>GitLab数据</h4>
            <div className="data-preview">
              <pre>{JSON.stringify(gitlabData, null, 2)}</pre>
            </div>
          </div>
        )}

        <div className="tool-section">
          <h4>快速链接</h4>
          <div className="link-grid">
            <a href="https://jira.dotfashion.cn" target="_blank" className="link-card">
              <span className="link-icon">🎯</span>
              <span className="link-text">JIRA系统</span>
            </a>
            <a href="https://gitlab.sheincorp.cn" target="_blank" className="link-card">
              <span className="link-icon">🦊</span>
              <span className="link-text">GitLab系统</span>
            </a>
            <a href="https://gitlab.sheincorp.cn/-/profile/personal_access_tokens" target="_blank" className="link-card">
              <span className="link-icon">🔑</span>
              <span className="link-text">访问令牌设置</span>
            </a>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ToolsTab;