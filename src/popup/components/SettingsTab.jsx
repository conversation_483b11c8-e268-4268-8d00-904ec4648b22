import React, { useState } from 'react';

const SettingsTab = ({ settings, onSaveSettings }) => {
  const [formData, setFormData] = useState({
    region: settings.region || 'cn',
    gitlabAccessToken: settings.gitlabAccessToken || ''
  });
  const [showToken, setShowToken] = useState(false);
  const [connectionStatus, setConnectionStatus] = useState('');
  const [testing, setTesting] = useState(false);

  const handleInputChange = (field, value) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleSave = async () => {
    try {
      await onSaveSettings(formData);
      setConnectionStatus('设置已保存');
      setTimeout(() => setConnectionStatus(''), 3000);
    } catch (error) {
      setConnectionStatus('保存失败: ' + error.message);
    }
  };

  const testConnection = async () => {
    if (!formData.gitlabAccessToken) {
      setConnectionStatus('请先输入GitLab访问令牌');
      return;
    }

    setTesting(true);
    setConnectionStatus('测试连接中...');

    try {
      const response = await chrome.runtime.sendMessage({
        action: 'fetchGitlabData',
        projectId: 10100, // 使用lpmpm项目测试
        endpoint: 'repository/branches'
      });

      if (response.success) {
        setConnectionStatus('✅ 连接成功！GitLab API可正常访问');
      } else {
        setConnectionStatus('❌ 连接失败: ' + response.error);
      }
    } catch (error) {
      setConnectionStatus('❌ 测试连接时出错: ' + error.message);
    } finally {
      setTesting(false);
    }
  };

  return (
    <div id="settings-tab">
      <div className="section">
        <h3>基础设置</h3>
        
        <div className="form-group">
          <label htmlFor="region-select">区域设置</label>
          <select 
            id="region-select" 
            className="form-control"
            value={formData.region}
            onChange={(e) => handleInputChange('region', e.target.value)}
          >
            <option value="cn">中国区 (CN)</option>
            <option value="wi">WI区</option>
            <option value="overseas">海外区</option>
          </select>
          <small className="form-text">选择对应的GitLab项目区域</small>
        </div>

        <div className="form-group">
          <label htmlFor="gitlab-token">GitLab访问令牌</label>
          <div className="input-group">
            <input 
              type={showToken ? 'text' : 'password'}
              id="gitlab-token" 
              className="form-control"
              placeholder="输入GitLab个人访问令牌"
              value={formData.gitlabAccessToken}
              onChange={(e) => handleInputChange('gitlabAccessToken', e.target.value)}
            />
            <button 
              type="button" 
              className="btn btn-secondary"
              onClick={() => setShowToken(!showToken)}
            >
              <span className="btn-icon">{showToken ? '🙈' : '👁️'}</span>
            </button>
          </div>
          <small className="form-text">
            在GitLab中：Preferences → Access Tokens → 创建令牌
            <a href="https://gitlab.sheincorp.cn/-/profile/personal_access_tokens" target="_blank" rel="noopener noreferrer">
              前往设置
            </a>
          </small>
        </div>

        <div className="form-actions">
          <button 
            className="btn btn-primary"
            onClick={handleSave}
          >
            保存设置
          </button>
          <button 
            className="btn btn-secondary"
            onClick={testConnection}
            disabled={testing || !formData.gitlabAccessToken}
          >
            {testing ? '测试中...' : '测试连接'}
          </button>
        </div>

        {connectionStatus && (
          <div className={`connection-status ${connectionStatus.includes('✅') ? 'success' : connectionStatus.includes('❌') ? 'error' : 'info'}`}>
            {connectionStatus}
          </div>
        )}
      </div>

      <div className="section">
        <h3>数据提取设置</h3>
        
        <div className="form-group">
          <label>默认提取字段</label>
          <div className="checkbox-group">
            {[
              { key: 'key', label: '问题编号' },
              { key: 'summary', label: '标题' },
              { key: 'status', label: '状态' },
              { key: 'assignee', label: '经办人' },
              { key: 'priority', label: '优先级' },
              { key: 'description', label: '描述' },
              { key: 'created', label: '创建时间' },
              { key: 'updated', label: '更新时间' }
            ].map(field => (
              <label key={field.key} className="checkbox-label">
                <input 
                  type="checkbox" 
                  defaultChecked={['key', 'summary', 'status', 'assignee', 'priority'].includes(field.key)}
                />
                <span>{field.label}</span>
              </label>
            ))}
          </div>
        </div>

        <div className="form-group">
          <label htmlFor="max-results">最大提取数量</label>
          <input 
            type="number" 
            id="max-results" 
            className="form-control"
            defaultValue="1000"
            min="1"
            max="10000"
          />
          <small className="form-text">单次提取的最大问题数量</small>
        </div>

        <div className="form-group">
          <label htmlFor="default-format">默认导出格式</label>
          <select id="default-format" className="form-control" defaultValue="csv">
            <option value="csv">CSV</option>
            <option value="json">JSON</option>
            <option value="excel">Excel</option>
          </select>
        </div>
      </div>

      <div className="section">
        <h3>通知设置</h3>
        
        <div className="form-group">
          <label className="checkbox-label">
            <input type="checkbox" defaultChecked />
            <span>显示操作通知</span>
          </label>
        </div>

        <div className="form-group">
          <label htmlFor="notification-duration">通知显示时长（秒）</label>
          <input 
            type="number" 
            id="notification-duration" 
            className="form-control"
            defaultValue="3"
            min="1"
            max="10"
          />
        </div>
      </div>
    </div>
  );
};

export default SettingsTab;