import React, { useState } from 'react';

/**
 * 任务列表组件
 * 显示任务列表并提供基础的任务状态切换功能
 */
const TaskList = ({ tasks, onTaskUpdate, onTaskDelete, onTaskSelect }) => {
  const [filter, setFilter] = useState('all');
  const [searchTerm, setSearchTerm] = useState('');

  // 任务状态标签映射
  const statusLabels = {
    todo: { text: '待办', color: '#ffc107' },
    in_progress: { text: '进行中', color: '#17a2b8' },
    done: { text: '已完成', color: '#28a745' },
    cancelled: { text: '已取消', color: '#6c757d' }
  };

  // 任务优先级标签映射
  const priorityLabels = {
    low: { text: '低', color: '#6c757d' },
    medium: { text: '中', color: '#17a2b8' },
    high: { text: '高', color: '#dc3545' }
  };

  // 过滤任务
  const filteredTasks = tasks.filter(task => {
    // 状态过滤
    if (filter !== 'all' && task.status !== filter) {
      return false;
    }
    
    // 搜索过滤
    if (searchTerm && !(
      task.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
      task.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
      (task.jiraKey && task.jiraKey.toLowerCase().includes(searchTerm.toLowerCase()))
    )) {
      return false;
    }
    
    return true;
  });

  // 更新任务状态
  const handleStatusChange = (taskId, newStatus) => {
    const task = tasks.find(t => t.id === taskId);
    if (task) {
      onTaskUpdate(taskId, { ...task, status: newStatus });
    }
  };

  // 格式化日期
  const formatDate = (dateString) => {
    if (!dateString) return '';
    const date = new Date(dateString);
    return date.toLocaleString('zh-CN', { 
      year: 'numeric', 
      month: '2-digit', 
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  return (
    <div className="task-list-container">
      <div className="task-list-header">
        <div className="search-filter">
          <input
            type="text"
            className="form-control"
            placeholder="搜索任务..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
          />
        </div>
        <div className="status-filter">
          <button 
            className={`filter-btn ${filter === 'all' ? 'active' : ''}`}
            onClick={() => setFilter('all')}
          >
            全部
          </button>
          <button 
            className={`filter-btn ${filter === 'todo' ? 'active' : ''}`}
            onClick={() => setFilter('todo')}
          >
            待办
          </button>
          <button 
            className={`filter-btn ${filter === 'in_progress' ? 'active' : ''}`}
            onClick={() => setFilter('in_progress')}
          >
            进行中
          </button>
          <button 
            className={`filter-btn ${filter === 'done' ? 'active' : ''}`}
            onClick={() => setFilter('done')}
          >
            已完成
          </button>
        </div>
      </div>

      <div className="task-count">
        共 <strong>{filteredTasks.length}</strong> 个任务
      </div>

      {filteredTasks.length === 0 ? (
        <div className="empty-state">
          <p>没有找到符合条件的任务</p>
          {searchTerm && <p>尝试使用不同的搜索词</p>}
          {filter !== 'all' && <p>或者切换到其他状态标签</p>}
        </div>
      ) : (
        <div className="task-items">
          {filteredTasks.map(task => (
            <div key={task.id} className={`task-item status-${task.status}`}>
              <div className="task-header">
                <div className="task-title" onClick={() => onTaskSelect(task)}>
                  {task.title}
                </div>
                <div className="task-actions">
                  <button 
                    className="btn-icon"
                    onClick={() => onTaskDelete(task.id)}
                    title="删除任务"
                  >
                    🗑️
                  </button>
                </div>
              </div>
              
              <div className="task-meta">
                {task.jiraKey && (
                  <span className="task-jira">{task.jiraKey}</span>
                )}
                <span 
                  className="task-priority"
                  style={{ backgroundColor: priorityLabels[task.priority]?.color }}
                >
                  {priorityLabels[task.priority]?.text}
                </span>
                <span 
                  className="task-status"
                  style={{ backgroundColor: statusLabels[task.status]?.color }}
                >
                  {statusLabels[task.status]?.text}
                </span>
              </div>
              
              {task.description && (
                <div className="task-description">
                  {task.description.length > 100 
                    ? `${task.description.substring(0, 100)}...` 
                    : task.description}
                </div>
              )}
              
              <div className="task-footer">
                <div className="task-dates">
                  {task.dueDate && (
                    <span className="task-due-date" title="截止日期">
                      📅 {formatDate(task.dueDate)}
                    </span>
                  )}
                  <span className="task-created" title="创建时间">
                    🕒 {formatDate(task.createdAt)}
                  </span>
                </div>
                
                <div className="task-status-actions">
                  {task.status !== 'todo' && (
                    <button 
                      className="status-btn todo"
                      onClick={() => handleStatusChange(task.id, 'todo')}
                    >
                      待办
                    </button>
                  )}
                  {task.status !== 'in_progress' && (
                    <button 
                      className="status-btn in-progress"
                      onClick={() => handleStatusChange(task.id, 'in_progress')}
                    >
                      进行中
                    </button>
                  )}
                  {task.status !== 'done' && (
                    <button 
                      className="status-btn done"
                      onClick={() => handleStatusChange(task.id, 'done')}
                    >
                      完成
                    </button>
                  )}
                  {task.status !== 'cancelled' && (
                    <button 
                      className="status-btn cancelled"
                      onClick={() => handleStatusChange(task.id, 'cancelled')}
                    >
                      取消
                    </button>
                  )}
                </div>
              </div>
            </div>
          ))}
        </div>
      )}
    </div>
  );
};

export default TaskList;