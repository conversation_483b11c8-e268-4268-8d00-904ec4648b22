import React from 'react';

const ExtractTab = ({ jiraStatus, extractedData, onExtractData, onRefreshStatus }) => {
  const handleCopyData = (format) => {
    if (extractedData.length === 0) return;

    let textToCopy = '';
    
    if (format === 'csv') {
      const headers = Object.keys(extractedData[0]).join(',');
      const rows = extractedData.map(item => 
        Object.values(item).map(value => 
          typeof value === 'string' && value.includes(',') 
            ? `"${value.replace(/"/g, '""')}"` 
            : value
        ).join(',')
      );
      textToCopy = [headers, ...rows].join('\n');
    } else if (format === 'json') {
      textToCopy = JSON.stringify(extractedData, null, 2);
    }

    navigator.clipboard.writeText(textToCopy).then(() => {
      // 可以添加成功提示
      console.log(`${format.toUpperCase()} 数据已复制到剪贴板`);
    }).catch(err => {
      console.error('复制失败:', err);
    });
  };

  const downloadCSV = () => {
    if (extractedData.length === 0) return;

    const headers = Object.keys(extractedData[0]).join(',');
    const rows = extractedData.map(item => 
      Object.values(item).map(value => 
        typeof value === 'string' && value.includes(',') 
          ? `"${value.replace(/"/g, '""')}"` 
          : value
      ).join(',')
    );
    const csvContent = [headers, ...rows].join('\n');

    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    const url = URL.createObjectURL(blob);
    link.setAttribute('href', url);
    link.setAttribute('download', `jira-data-${new Date().toISOString().split('T')[0]}.csv`);
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  return (
    <div id="extract-tab">
      <div className="section">
        <h3>JIRA数据提取</h3>
        <div className="status-info" id="jira-status">
          <span className={`status-dot ${!jiraStatus.isJiraPage ? 'warning' : ''}`}></span>
          <span className="status-text">{jiraStatus.statusText}</span>
        </div>
        
        <div className="action-buttons">
          <button 
            className="btn btn-primary" 
            onClick={() => onExtractData('current')}
            disabled={!jiraStatus.isJiraPage}
          >
            <span className="btn-icon">📊</span>
            提取当前页面数据
          </button>
          <button 
            className="btn btn-secondary" 
            onClick={() => onExtractData('filter')}
            disabled={!jiraStatus.isFilterPage}
          >
            <span className="btn-icon">🔍</span>
            提取筛选器数据
          </button>
        </div>

        {extractedData.length > 0 && (
          <div className="extracted-data">
            <h4>提取结果 <span className="data-count">({extractedData.length} 条)</span></h4>
            <div className="data-preview">
              {extractedData.slice(0, 3).map((item, index) => (
                <div key={index} className="data-item">
                  <strong>{item.key}</strong>: {item.summary}
                </div>
              ))}
              {extractedData.length > 3 && (
                <div className="data-item">... 还有 {extractedData.length - 3} 条数据</div>
              )}
            </div>
            <div className="export-options">
              <button className="btn btn-small" onClick={() => handleCopyData('csv')}>
                复制CSV
              </button>
              <button className="btn btn-small" onClick={() => handleCopyData('json')}>
                复制JSON
              </button>
              <button className="btn btn-small" onClick={downloadCSV}>
                下载CSV
              </button>
            </div>
          </div>
        )}

        <div className="quick-actions">
          <h4>快速操作</h4>
          <div className="action-grid">
            <button className="action-card" onClick={onRefreshStatus}>
              <span className="action-icon">🔄</span>
              <span className="action-text">刷新页面状态</span>
            </button>
            <button className="action-card" onClick={() => chrome.tabs.create({ url: 'https://jira.dotfashion.cn' })}>
              <span className="action-icon">🔗</span>
              <span className="action-text">打开JIRA</span>
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ExtractTab;