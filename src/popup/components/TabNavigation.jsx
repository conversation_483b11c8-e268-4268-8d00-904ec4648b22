import React from 'react';

const TabNavigation = ({ activeTab, onTabChange }) => {
  const tabs = [
    { id: 'tasks', label: '任务' },
    { id: 'extract', label: '数据提取' },
    { id: 'tools', label: '工具箱' },
    { id: 'release', label: '临时发版' },
    { id: 'settings', label: '设置' }
  ];

  return (
    <nav className="nav-tabs">
      {tabs.map(tab => (
        <button
          key={tab.id}
          className={`tab-btn ${activeTab === tab.id ? 'active' : ''}`}
          onClick={() => onTabChange(tab.id)}
        >
          {tab.label}
        </button>
      ))}
    </nav>
  );
};

export default TabNavigation;