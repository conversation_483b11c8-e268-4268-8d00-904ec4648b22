/* DutyRobot Dashboard Styles */

:root {
  --primary-color: #4a6cf7;
  --primary-hover: #3a5ce5;
  --secondary-color: #6c757d;
  --success-color: #28a745;
  --danger-color: #dc3545;
  --warning-color: #ffc107;
  --info-color: #17a2b8;
  --light-color: #f8f9fa;
  --dark-color: #343a40;
  --body-bg: #f5f7fb;
  --card-bg: #ffffff;
  --border-color: #e4e7ed;
  --text-color: #333;
  --text-muted: #6c757d;
  --shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

body {
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  background-color: var(--body-bg);
  color: var(--text-color);
  line-height: 1.6;
}

/* Dashboard Layout */
.dashboard-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
}

.dashboard-header {
  text-align: center;
  margin-bottom: 30px;
  padding-bottom: 20px;
  border-bottom: 1px solid var(--border-color);
}

.dashboard-header h1 {
  font-size: 2.2rem;
  color: var(--primary-color);
  margin-bottom: 5px;
}

.subtitle {
  color: var(--text-muted);
  font-size: 1rem;
}

/* Navigation */
.dashboard-nav {
  display: flex;
  justify-content: center;
  margin-bottom: 30px;
  gap: 10px;
}

.nav-btn {
  padding: 10px 20px;
  background-color: var(--light-color);
  border: 1px solid var(--border-color);
  border-radius: 5px;
  cursor: pointer;
  font-size: 1rem;
  transition: all 0.2s ease;
}

.nav-btn:hover {
  background-color: #e9ecef;
}

.nav-btn.active {
  background-color: var(--primary-color);
  color: white;
  border-color: var(--primary-color);
}

/* Dashboard Content */
.dashboard-content {
  background-color: var(--card-bg);
  border-radius: 8px;
  box-shadow: var(--shadow);
  padding: 30px;
  min-height: 500px;
}

/* Section Headers */
.dashboard-content h2 {
  color: var(--primary-color);
  margin-bottom: 20px;
  padding-bottom: 10px;
  border-bottom: 1px solid var(--border-color);
}

.dashboard-content h3 {
  color: var(--dark-color);
  margin: 20px 0 15px;
}

/* Forms */
.form-group {
  margin-bottom: 20px;
}

.form-row {
  display: flex;
  gap: 20px;
}

.form-row .form-group {
  flex: 1;
}

label {
  display: block;
  margin-bottom: 8px;
  font-weight: 500;
}

.form-control {
  width: 100%;
  padding: 10px;
  border: 1px solid var(--border-color);
  border-radius: 4px;
  font-size: 1rem;
}

.form-control:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(74, 108, 247, 0.2);
}

textarea.form-control {
  min-height: 100px;
  resize: vertical;
}

.form-check {
  display: flex;
  align-items: center;
  margin-bottom: 15px;
}

.form-check-input {
  margin-right: 10px;
}

.form-actions {
  margin-top: 30px;
  display: flex;
  gap: 10px;
}

/* Buttons */
.btn {
  padding: 10px 20px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 1rem;
  transition: all 0.2s ease;
}

.btn-sm {
  padding: 5px 10px;
  font-size: 0.875rem;
}

.btn-primary {
  background-color: var(--primary-color);
  color: white;
}

.btn-primary:hover {
  background-color: var(--primary-hover);
}

.btn-secondary {
  background-color: var(--secondary-color);
  color: white;
}

.btn-secondary:hover {
  background-color: #5a6268;
}

.btn-danger {
  background-color: var(--danger-color);
  color: white;
}

.btn-danger:hover {
  background-color: #c82333;
}

.btn-link {
  background-color: transparent;
  color: var(--primary-color);
  text-decoration: none;
  padding: 5px 10px;
}

.btn-link:hover {
  text-decoration: underline;
}

/* Settings Groups */
.settings-group {
  margin-bottom: 40px;
  padding-bottom: 20px;
  border-bottom: 1px solid var(--border-color);
}

.settings-group:last-child {
  border-bottom: none;
}

/* Tasks */
.task-form {
  margin-bottom: 30px;
  padding: 20px;
  background-color: #f8f9fa;
  border-radius: 8px;
}

.task-list {
  margin-top: 30px;
}

.tasks {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 20px;
}

.task-card {
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  padding: 20px;
  border-left: 4px solid #ccc;
}

.task-card.priority-high {
  border-left-color: var(--danger-color);
}

.task-card.priority-medium {
  border-left-color: var(--warning-color);
}

.task-card.priority-low {
  border-left-color: var(--info-color);
}

.task-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.task-header h4 {
  margin: 0;
  font-size: 1.1rem;
}

.status-badge {
  padding: 3px 8px;
  border-radius: 12px;
  font-size: 0.75rem;
  font-weight: 500;
}

.status-pending {
  background-color: #e9ecef;
  color: #495057;
}

.status-in-progress {
  background-color: #cfe2ff;
  color: #084298;
}

.status-completed {
  background-color: #d1e7dd;
  color: #0f5132;
}

.status-cancelled {
  background-color: #f8d7da;
  color: #842029;
}

.task-description {
  margin: 10px 0;
  color: #6c757d;
  font-size: 0.9rem;
}

.task-meta {
  display: flex;
  justify-content: space-between;
  font-size: 0.8rem;
  color: #6c757d;
  margin-bottom: 15px;
}

.task-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 15px;
}

.status-select {
  padding: 5px;
  border-radius: 4px;
  border: 1px solid var(--border-color);
}

.empty-state {
  text-align: center;
  padding: 30px;
  color: var(--text-muted);
  font-style: italic;
}

/* Tools Section */
.tools-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  gap: 20px;
}

.tool-card {
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  padding: 20px;
  text-align: center;
}

.tool-card h3 {
  color: var(--primary-color);
  margin-bottom: 10px;
}

.tool-card p {
  color: var(--text-muted);
  margin-bottom: 15px;
  font-size: 0.9rem;
}

.quick-links {
  display: flex;
  justify-content: center;
  gap: 10px;
  margin-top: 15px;
}

/* Loading State */
.loading {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 300px;
  font-size: 1.2rem;
  color: var(--text-muted);
}

/* Footer */
.dashboard-footer {
  margin-top: 40px;
  text-align: center;
  color: var(--text-muted);
  font-size: 0.9rem;
  padding-top: 20px;
  border-top: 1px solid var(--border-color);
}

/* Responsive Adjustments */
@media (max-width: 768px) {
  .dashboard-container {
    padding: 15px;
  }
  
  .dashboard-content {
    padding: 20px;
  }
  
  .form-row {
    flex-direction: column;
    gap: 0;
  }
  
  .tasks {
    grid-template-columns: 1fr;
  }
  
  .tools-grid {
    grid-template-columns: 1fr;
  }
}