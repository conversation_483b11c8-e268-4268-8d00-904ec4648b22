import React, { useState, useEffect } from 'react';

const DashboardApp = () => {
  const [activeTab, setActiveTab] = useState('tasks');
  const [settings, setSettings] = useState({});
  const [tasks, setTasks] = useState([]);
  const [loading, setLoading] = useState(true);
  
  // 加载设置和任务数据
  useEffect(() => {
    const loadData = async () => {
      try {
        // 加载设置
        const storedSettings = await new Promise((resolve) => {
          chrome.storage.sync.get(['gitlabSettings', 'jiraSettings', 'notificationSettings'], (result) => {
            resolve({
              gitlab: result.gitlabSettings || {},
              jira: result.jiraSettings || {},
              notifications: result.notificationSettings || {}
            });
          });
        });
        
        // 加载任务
        const storedTasks = await new Promise((resolve) => {
          chrome.storage.local.get(['tasks'], (result) => {
            resolve(result.tasks || []);
          });
        });
        
        setSettings(storedSettings);
        setTasks(storedTasks);
      } catch (error) {
        console.error('加载数据失败:', error);
      } finally {
        setLoading(false);
      }
    };
    
    loadData();
  }, []);
  
  // 保存设置
  const saveSettings = async (newSettings) => {
    try {
      await new Promise((resolve) => {
        chrome.storage.sync.set({
          gitlabSettings: newSettings.gitlab,
          jiraSettings: newSettings.jira,
          notificationSettings: newSettings.notifications
        }, resolve);
      });
      
      setSettings(newSettings);
      alert('设置已保存');
    } catch (error) {
      console.error('保存设置失败:', error);
      alert('保存设置失败');
    }
  };
  
  // 保存任务
  const saveTasks = async (newTasks) => {
    try {
      await new Promise((resolve) => {
        chrome.storage.local.set({ tasks: newTasks }, resolve);
      });
      
      setTasks(newTasks);
    } catch (error) {
      console.error('保存任务失败:', error);
      alert('保存任务失败');
    }
  };
  
  // 添加任务
  const addTask = (task) => {
    const newTasks = [...tasks, { ...task, id: Date.now() }];
    saveTasks(newTasks);
  };
  
  // 更新任务
  const updateTask = (id, updatedTask) => {
    const newTasks = tasks.map(task => 
      task.id === id ? { ...task, ...updatedTask } : task
    );
    saveTasks(newTasks);
  };
  
  // 删除任务
  const deleteTask = (id) => {
    const newTasks = tasks.filter(task => task.id !== id);
    saveTasks(newTasks);
  };
  
  // 测试GitLab连接
  const testGitLabConnection = async () => {
    try {
      const { gitlab } = settings;
      if (!gitlab.apiUrl || !gitlab.token) {
        alert('请先填写GitLab API地址和访问令牌');
        return;
      }
      
      const response = await fetch(`${gitlab.apiUrl}/projects?per_page=1`, {
        headers: {
          'PRIVATE-TOKEN': gitlab.token
        }
      });
      
      if (response.ok) {
        alert('GitLab连接成功!');
      } else {
        alert(`GitLab连接失败: ${response.status} ${response.statusText}`);
      }
    } catch (error) {
      console.error('测试GitLab连接失败:', error);
      alert(`测试GitLab连接失败: ${error.message}`);
    }
  };
  
  if (loading) {
    return <div className="loading">加载中...</div>;
  }
  
  return (
    <div className="dashboard-container">
      <header className="dashboard-header">
        <h1>🤖 值班萝卜 - 工作台</h1>
        <p className="subtitle">任务管理与设置</p>
      </header>
      
      <nav className="dashboard-nav">
        <button 
          className={`nav-btn ${activeTab === 'tasks' ? 'active' : ''}`}
          onClick={() => setActiveTab('tasks')}
        >
          任务管理
        </button>
        <button 
          className={`nav-btn ${activeTab === 'settings' ? 'active' : ''}`}
          onClick={() => setActiveTab('settings')}
        >
          设置
        </button>
        <button 
          className={`nav-btn ${activeTab === 'tools' ? 'active' : ''}`}
          onClick={() => setActiveTab('tools')}
        >
          工具箱
        </button>
      </nav>
      
      <main className="dashboard-content">
        {activeTab === 'tasks' && (
          <div className="tasks-section">
            <h2>任务管理</h2>
            
            <div className="task-form">
              <h3>添加新任务</h3>
              <form onSubmit={(e) => {
                e.preventDefault();
                const formData = new FormData(e.target);
                addTask({
                  title: formData.get('title'),
                  description: formData.get('description'),
                  dueDate: formData.get('dueDate'),
                  priority: formData.get('priority'),
                  status: 'pending',
                  createdAt: new Date().toISOString()
                });
                e.target.reset();
              }}>
                <div className="form-group">
                  <label htmlFor="title">标题</label>
                  <input type="text" id="title" name="title" required className="form-control" />
                </div>
                
                <div className="form-group">
                  <label htmlFor="description">描述</label>
                  <textarea id="description" name="description" className="form-control"></textarea>
                </div>
                
                <div className="form-row">
                  <div className="form-group">
                    <label htmlFor="dueDate">截止日期</label>
                    <input type="date" id="dueDate" name="dueDate" className="form-control" />
                  </div>
                  
                  <div className="form-group">
                    <label htmlFor="priority">优先级</label>
                    <select id="priority" name="priority" className="form-control">
                      <option value="low">低</option>
                      <option value="medium">中</option>
                      <option value="high">高</option>
                    </select>
                  </div>
                </div>
                
                <button type="submit" className="btn btn-primary">添加任务</button>
              </form>
            </div>
            
            <div className="task-list">
              <h3>任务列表</h3>
              {tasks.length === 0 ? (
                <p className="empty-state">暂无任务</p>
              ) : (
                <div className="tasks">
                  {tasks.map(task => (
                    <div key={task.id} className={`task-card priority-${task.priority}`}>
                      <div className="task-header">
                        <h4>{task.title}</h4>
                        <span className={`status-badge status-${task.status}`}>
                          {task.status === 'pending' ? '待处理' : 
                           task.status === 'in-progress' ? '进行中' : 
                           task.status === 'completed' ? '已完成' : '已取消'}
                        </span>
                      </div>
                      
                      <p className="task-description">{task.description}</p>
                      
                      <div className="task-meta">
                        {task.dueDate && (
                          <span className="due-date">
                            截止日期: {new Date(task.dueDate).toLocaleDateString()}
                          </span>
                        )}
                        <span className="priority">
                          优先级: {task.priority === 'low' ? '低' : 
                                  task.priority === 'medium' ? '中' : '高'}
                        </span>
                      </div>
                      
                      <div className="task-actions">
                        <select 
                          value={task.status}
                          onChange={(e) => updateTask(task.id, { status: e.target.value })}
                          className="status-select"
                        >
                          <option value="pending">待处理</option>
                          <option value="in-progress">进行中</option>
                          <option value="completed">已完成</option>
                          <option value="cancelled">已取消</option>
                        </select>
                        
                        <button 
                          className="btn btn-danger btn-sm"
                          onClick={() => deleteTask(task.id)}
                        >
                          删除
                        </button>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>
          </div>
        )}
        
        {activeTab === 'settings' && (
          <div className="settings-section">
            <h2>设置</h2>
            
            <form onSubmit={(e) => {
              e.preventDefault();
              const formData = new FormData(e.target);
              
              const newSettings = {
                gitlab: {
                  apiUrl: formData.get('gitlab-api-url'),
                  token: formData.get('gitlab-token'),
                  projectId: formData.get('gitlab-project-id')
                },
                jira: {
                  baseUrl: formData.get('jira-base-url'),
                  username: formData.get('jira-username'),
                  apiToken: formData.get('jira-api-token'),
                  defaultProject: formData.get('jira-default-project')
                },
                notifications: {
                  enabled: formData.get('notifications-enabled') === 'on',
                  sound: formData.get('notification-sound') === 'on',
                  taskReminders: formData.get('task-reminders') === 'on',
                  reminderTime: formData.get('reminder-time')
                }
              };
              
              saveSettings(newSettings);
            }}>
              <div className="settings-group">
                <h3>GitLab 设置</h3>
                
                <div className="form-group">
                  <label htmlFor="gitlab-api-url">GitLab API地址</label>
                  <input 
                    type="url" 
                    id="gitlab-api-url" 
                    name="gitlab-api-url" 
                    className="form-control"
                    defaultValue={settings.gitlab?.apiUrl || 'https://gitlab.sheincorp.cn/api/v4'}
                    placeholder="https://gitlab.sheincorp.cn/api/v4"
                  />
                </div>
                
                <div className="form-group">
                  <label htmlFor="gitlab-token">GitLab访问令牌</label>
                  <input 
                    type="password" 
                    id="gitlab-token" 
                    name="gitlab-token" 
                    className="form-control"
                    defaultValue={settings.gitlab?.token || ''}
                    placeholder="输入GitLab个人访问令牌"
                  />
                </div>
                
                <div className="form-group">
                  <label htmlFor="gitlab-project-id">GitLab项目ID</label>
                  <input 
                    type="text" 
                    id="gitlab-project-id" 
                    name="gitlab-project-id" 
                    className="form-control"
                    defaultValue={settings.gitlab?.projectId || '10100'}
                    placeholder="项目ID，例如：10100"
                  />
                </div>
                
                <button 
                  type="button" 
                  className="btn btn-secondary"
                  onClick={testGitLabConnection}
                >
                  测试连接
                </button>
              </div>
              
              <div className="settings-group">
                <h3>JIRA 设置</h3>
                
                <div className="form-group">
                  <label htmlFor="jira-base-url">JIRA基础URL</label>
                  <input 
                    type="url" 
                    id="jira-base-url" 
                    name="jira-base-url" 
                    className="form-control"
                    defaultValue={settings.jira?.baseUrl || 'https://jira.dotfashion.cn'}
                    placeholder="https://jira.dotfashion.cn"
                  />
                </div>
                
                <div className="form-group">
                  <label htmlFor="jira-username">JIRA用户名</label>
                  <input 
                    type="text" 
                    id="jira-username" 
                    name="jira-username" 
                    className="form-control"
                    defaultValue={settings.jira?.username || ''}
                    placeholder="JIRA用户名"
                  />
                </div>
                
                <div className="form-group">
                  <label htmlFor="jira-api-token">JIRA API令牌</label>
                  <input 
                    type="password" 
                    id="jira-api-token" 
                    name="jira-api-token" 
                    className="form-control"
                    defaultValue={settings.jira?.apiToken || ''}
                    placeholder="JIRA API令牌"
                  />
                </div>
                
                <div className="form-group">
                  <label htmlFor="jira-default-project">默认项目</label>
                  <input 
                    type="text" 
                    id="jira-default-project" 
                    name="jira-default-project" 
                    className="form-control"
                    defaultValue={settings.jira?.defaultProject || 'LPMPM'}
                    placeholder="默认JIRA项目键"
                  />
                </div>
              </div>
              
              <div className="settings-group">
                <h3>通知设置</h3>
                
                <div className="form-check">
                  <input 
                    type="checkbox" 
                    id="notifications-enabled" 
                    name="notifications-enabled" 
                    className="form-check-input"
                    defaultChecked={settings.notifications?.enabled}
                  />
                  <label htmlFor="notifications-enabled" className="form-check-label">
                    启用通知
                  </label>
                </div>
                
                <div className="form-check">
                  <input 
                    type="checkbox" 
                    id="notification-sound" 
                    name="notification-sound" 
                    className="form-check-input"
                    defaultChecked={settings.notifications?.sound}
                  />
                  <label htmlFor="notification-sound" className="form-check-label">
                    通知声音
                  </label>
                </div>
                
                <div className="form-check">
                  <input 
                    type="checkbox" 
                    id="task-reminders" 
                    name="task-reminders" 
                    className="form-check-input"
                    defaultChecked={settings.notifications?.taskReminders}
                  />
                  <label htmlFor="task-reminders" className="form-check-label">
                    任务提醒
                  </label>
                </div>
                
                <div className="form-group">
                  <label htmlFor="reminder-time">提醒时间（分钟）</label>
                  <input 
                    type="number" 
                    id="reminder-time" 
                    name="reminder-time" 
                    className="form-control"
                    defaultValue={settings.notifications?.reminderTime || 30}
                    min="5"
                    max="1440"
                  />
                </div>
              </div>
              
              <div className="form-actions">
                <button type="submit" className="btn btn-primary">保存设置</button>
                <button type="reset" className="btn btn-secondary">重置</button>
              </div>
            </form>
          </div>
        )}
        
        {activeTab === 'tools' && (
          <div className="tools-section">
            <h2>工具箱</h2>
            
            <div className="tools-grid">
              <div className="tool-card">
                <h3>JIRA数据提取</h3>
                <p>从JIRA页面提取问题数据，支持单个问题和筛选器结果。</p>
                <button className="btn btn-primary" onClick={() => {
                  chrome.tabs.query({active: true, currentWindow: true}, (tabs) => {
                    const currentTab = tabs[0];
                    if (currentTab && currentTab.url.includes('jira.dotfashion.cn')) {
                      chrome.tabs.sendMessage(currentTab.id, { action: 'extractJiraData' });
                    } else {
                      alert('请先打开JIRA页面');
                    }
                  });
                }}>
                  打开JIRA提取器
                </button>
              </div>
              
              <div className="tool-card">
                <h3>GitLab MR检查</h3>
                <p>检查GitLab合并请求状态，包括CI/CD管道状态和评论。</p>
                <button className="btn btn-primary" onClick={() => {
                  // 实现GitLab MR检查功能
                  alert('GitLab MR检查功能即将推出');
                }}>
                  检查MR状态
                </button>
              </div>
              
              <div className="tool-card">
                <h3>值班报告生成</h3>
                <p>根据收集的数据自动生成值班报告，支持多种格式。</p>
                <button className="btn btn-primary" onClick={() => {
                  // 实现值班报告生成功能
                  alert('值班报告生成功能即将推出');
                }}>
                  生成报告
                </button>
              </div>
              
              <div className="tool-card">
                <h3>快速链接</h3>
                <p>常用系统的快速访问链接。</p>
                <div className="quick-links">
                  <a href="https://jira.dotfashion.cn" target="_blank" rel="noopener noreferrer" className="btn btn-link">
                    JIRA
                  </a>
                  <a href="https://gitlab.sheincorp.cn" target="_blank" rel="noopener noreferrer" className="btn btn-link">
                    GitLab
                  </a>
                  <a href="https://confluence.dotfashion.cn" target="_blank" rel="noopener noreferrer" className="btn btn-link">
                    Confluence
                  </a>
                </div>
              </div>
            </div>
          </div>
        )}
      </main>
      
      <footer className="dashboard-footer">
        <p>DutyRobot Browser Extension &copy; {new Date().getFullYear()}</p>
      </footer>
    </div>
  );
};

export default DashboardApp;