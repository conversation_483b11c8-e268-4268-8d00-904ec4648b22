// DutyRobot JIRA Content Script

(function() {
  'use strict';
  
  console.log('DutyRobot JIRA Content Script loaded');
  
  // Message listener for background script communication
  chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
    console.log('Content script received message:', request);
    
    switch (request.action) {
      case 'extractData':
        handleExtractData(request, sendResponse);
        return true; // Keep the message channel open for async response
        
      case 'extractJiraData':
        handleExtractJiraData(request, sendResponse);
        return true; // Keep the message channel open for async response
        
      case 'checkPage':
        handleCheckPage(sendResponse);
        return true;
        
      default:
        console.log('Unknown action:', request.action);
        sendResponse({ success: false, error: 'Unknown action' });
    }
  });
  
  // Handle data extraction
  async function handleExtractData(request, sendResponse) {
    try {
      const extractType = request.extractType || 'current';
      let extractedData = [];
      
      // Determine extraction method based on page type and extract type
      if (extractType === 'selection') {
        extractedData = extractSelectedIssues();
      } else if (isIssueListPage()) {
        extractedData = extractIssueListData();
      } else if (isSingleIssuePage()) {
        extractedData = extractSingleIssueData();
      } else {
        sendResponse({ 
          success: false, 
          error: 'This page does not contain JIRA issues to extract' 
        });
        return;
      }
      
      if (extractedData.length > 0) {
        sendResponse({ 
          success: true, 
          data: extractedData,
          extractType: extractType,
          pageType: getPageType(),
          timestamp: new Date().toISOString()
        });
        
        // Show success notification on page
        showPageNotification(`Successfully extracted ${extractedData.length} issues`, 'success');
      } else {
        sendResponse({ 
          success: false, 
          error: 'No JIRA issues found on this page' 
        });
        showPageNotification('No JIRA issues found on this page', 'warning');
      }
    } catch (error) {
      console.error('Error extracting data:', error);
      sendResponse({ 
        success: false, 
        error: error.message || 'Failed to extract data' 
      });
      showPageNotification('Failed to extract data: ' + error.message, 'error');
    }
  }
  
  // Handle page check
  function handleCheckPage(sendResponse) {
    const pageInfo = {
      isJiraPage: isJiraPage(),
      pageType: getPageType(),
      issueCount: getIssueCount(),
      url: window.location.href,
      title: document.title
    };
    
    sendResponse({ success: true, pageInfo });
  }
  
  // Check if current page is a JIRA page
  function isJiraPage() {
    const url = window.location.href;
    const hostname = window.location.hostname;
    
    return (
      hostname.includes('atlassian.net') ||
      hostname.includes('jira.') ||
      url.includes('/jira/') ||
      url.includes('/browse/') ||
      url.includes('/issues/') ||
      document.querySelector('[data-testid="issue.views.issue-base.foundation.breadcrumbs.breadcrumb-current-issue-container"]') !== null ||
      document.querySelector('.issue-header') !== null ||
      document.querySelector('.navigator-content') !== null
    );
  }
  
  // Get page type
  function getPageType() {
    if (isSingleIssuePage()) {
      return 'single-issue';
    } else if (isIssueListPage()) {
      return 'issue-list';
    } else if (isJiraPage()) {
      return 'jira-other';
    }
    return 'unknown';
  }
  
  // Check if current page is a single issue page
  function isSingleIssuePage() {
    return (
      document.querySelector('[data-testid="issue.views.issue-base.foundation.breadcrumbs.breadcrumb-current-issue-container"]') !== null ||
      document.querySelector('.issue-header') !== null ||
      document.querySelector('#key-val') !== null ||
      window.location.href.includes('/browse/')
    );
  }
  
  // Check if current page is an issue list page
  function isIssueListPage() {
    return (
      document.querySelector('.navigator-content') !== null ||
      document.querySelector('[data-testid="platform-board-kit.ui.board.scroll.board-scroll"]') !== null ||
      document.querySelector('.js-issue') !== null ||
      document.querySelector('[data-testid="issue-table"]') !== null ||
      window.location.href.includes('/issues/') ||
      window.location.href.includes('/projects/') && window.location.href.includes('/board')
    );
  }
  
  // Get issue count on current page
  function getIssueCount() {
    if (isSingleIssuePage()) {
      return 1;
    } else if (isIssueListPage()) {
      const issues = document.querySelectorAll('.js-issue, [data-testid="platform-board-kit.ui.card.card"], tr[data-issuekey]');
      return issues.length;
    }
    return 0;
  }
  
  // Extract data from issue list page
  function extractIssueListData() {
    const extractedData = [];
    
    // Try different selectors for different JIRA versions
    const issueSelectors = [
      '.js-issue', // Classic JIRA
      '[data-testid="platform-board-kit.ui.card.card"]', // New JIRA board
      'tr[data-issuekey]', // Table view
      '[data-testid="issue-table"] tr[data-testid]' // New table view
    ];
    
    let issues = [];
    for (const selector of issueSelectors) {
      issues = document.querySelectorAll(selector);
      if (issues.length > 0) break;
    }
    
    issues.forEach((issue, index) => {
      try {
        const issueData = extractIssueFromElement(issue);
        if (issueData && issueData.issueKey) {
          extractedData.push(issueData);
        }
      } catch (error) {
        console.warn(`Error extracting issue ${index}:`, error);
      }
    });
    
    return extractedData;
  }
  
  // Extract data from single issue page
  function extractSingleIssueData() {
    try {
      const issueData = extractSingleIssueFromPage();
      return issueData ? [issueData] : [];
    } catch (error) {
      console.error('Error extracting single issue:', error);
      return [];
    }
  }
  
  // Extract selected issues
  function extractSelectedIssues() {
    const selection = window.getSelection();
    if (!selection.rangeCount) return [];
    
    const range = selection.getRangeAt(0);
    const container = range.commonAncestorContainer;
    const parentElement = container.nodeType === Node.TEXT_NODE ? container.parentElement : container;
    
    // Find all issue elements within the selection
    const issueElements = parentElement.querySelectorAll('.js-issue, [data-testid="platform-board-kit.ui.card.card"], tr[data-issuekey]');
    const extractedData = [];
    
    issueElements.forEach(issue => {
      try {
        const issueData = extractIssueFromElement(issue);
        if (issueData && issueData.issueKey) {
          extractedData.push(issueData);
        }
      } catch (error) {
        console.warn('Error extracting selected issue:', error);
      }
    });
    
    return extractedData;
  }
  
  // Extract issue data from DOM element
  function extractIssueFromElement(element) {
    const issueData = {
      issueKey: '',
      summary: '',
      status: '',
      assignee: '',
      priority: '',
      issueType: '',
      created: '',
      updated: '',
      description: '',
      comments: ''
    };
    
    try {
      // Extract issue key
      const keySelectors = [
        '[data-issuekey]',
        '.issue-link',
        '[data-testid="issue.issue-view.views.common.issue-line-card.issue-line-card-view.key"]',
        '.ghx-key',
        '.issuekey',
        'a[href*="/browse/"]',
        '[data-test-id*="issue-key"]',
        '[aria-label*="issue key"]'
      ];
      
      for (const selector of keySelectors) {
        const keyElement = element.querySelector(selector) || (element.matches(selector) ? element : null);
        if (keyElement) {
          const keyFromAttr = keyElement.getAttribute('data-issuekey');
          const keyFromHref = keyElement.getAttribute('href')?.match(/[A-Z]+-\d+/)?.[0];
          const keyFromText = keyElement.textContent?.match(/[A-Z]+-\d+/)?.[0];
          
          issueData.issueKey = keyFromAttr || keyFromHref || keyFromText || '';
          if (issueData.issueKey) break;
        }
      }
      
      // 如果还没找到issue key，尝试在整个元素中查找
      if (!issueData.issueKey) {
        const textContent = element.textContent || '';
        const keyMatch = textContent.match(/[A-Z]+-\d+/);
        if (keyMatch) {
          issueData.issueKey = keyMatch[0];
        }
      }
      
      // Extract summary
      const summarySelectors = [
        '.summary',
        '[data-testid="issue.issue-view.views.common.issue-line-card.issue-line-card-view.summary"]',
        '.ghx-summary',
        '.issue-link-summary',
        '[data-test-id*="summary"]',
        '[aria-label*="summary"]',
        '.issue-title',
        '.issue-content-container'
      ];
      
      for (const selector of summarySelectors) {
        const summaryElement = element.querySelector(selector);
        if (summaryElement) {
          issueData.summary = summaryElement.textContent?.trim() || '';
          break;
        }
      }
      
      // Extract status
      const statusSelectors = [
        '.status',
        '[data-testid="issue.issue-view.views.common.issue-line-card.issue-line-card-view.status"]',
        '.ghx-status',
        '.issue-status',
        '[data-test-id*="status"]',
        '[aria-label*="status"]',
        '.status-field',
        '.status-indicator'
      ];
      
      for (const selector of statusSelectors) {
        const statusElement = element.querySelector(selector);
        if (statusElement) {
          issueData.status = statusElement.textContent?.trim() || '';
          break;
        }
      }
      
      // Extract assignee
      const assigneeSelectors = [
        '.assignee',
        '[data-testid="issue.issue-view.views.common.issue-line-card.issue-line-card-view.assignee"]',
        '.ghx-assignee',
        '.issue-assignee',
        '[data-test-id*="assignee"]',
        '[aria-label*="assignee"]',
        '.assignee-field',
        'img[alt*="Assignee"]'
      ];
      
      for (const selector of assigneeSelectors) {
        const assigneeElement = element.querySelector(selector);
        if (assigneeElement) {
          issueData.assignee = assigneeElement.textContent?.trim() || 
                              assigneeElement.getAttribute('title') || 
                              assigneeElement.getAttribute('alt')?.replace('Assignee:', '')?.trim() || '';
          break;
        }
      }
    } catch (error) {
      console.warn('Error extracting issue data from element:', error);
    }
    
    try {
      // Extract priority
      const prioritySelectors = [
        '.priority',
        '[data-testid="issue.issue-view.views.common.issue-line-card.issue-line-card-view.priority"]',
        '.ghx-priority',
        '.issue-priority',
        '[data-test-id*="priority"]',
        '[aria-label*="priority"]',
        'img[alt*="Priority"]',
        '.priority-field'
      ];
      
      for (const selector of prioritySelectors) {
        const priorityElement = element.querySelector(selector);
        if (priorityElement) {
          issueData.priority = priorityElement.textContent?.trim() || 
                              priorityElement.getAttribute('title') || 
                              priorityElement.getAttribute('alt')?.replace('Priority:', '')?.trim() || '';
          break;
        }
      }
      
      // Extract issue type
      const typeSelectors = [
        '.issuetype',
        '[data-testid="issue.issue-view.views.common.issue-line-card.issue-line-card-view.issue-type"]',
        '.ghx-type',
        '.issue-type',
        '[data-test-id*="issue-type"]',
        '[aria-label*="issue type"]',
        'img[alt*="Issue Type"]',
        '.type-field'
      ];
      
      for (const selector of typeSelectors) {
        const typeElement = element.querySelector(selector);
        if (typeElement) {
          issueData.issueType = typeElement.textContent?.trim() || 
                               typeElement.getAttribute('title') || 
                               typeElement.getAttribute('alt')?.replace('Issue Type:', '')?.trim() || '';
          break;
        }
      }
    } catch (error) {
      console.warn('Error extracting additional issue data:', error);
    }
    
    return issueData;
  }
  
  // Extract single issue data from page
  function extractSingleIssueFromPage() {
    const issueData = {
      issueKey: '',
      summary: '',
      status: '',
      assignee: '',
      priority: '',
      issueType: '',
      created: '',
      updated: '',
      description: '',
      comments: ''
    };
    
    try {
      // Extract issue key
      const keySelectors = [
        '[data-testid="issue.views.issue-base.foundation.breadcrumbs.breadcrumb-current-issue-container"]',
        '#key-val',
        '.issue-header-key',
        '[data-testid="issue.views.issue-base.foundation.summary.heading"]',
        '[data-test-id*="issue-key"]',
        '.issue-key',
        'a[href*="/browse/"]',
        'h1 + div', // 新版JIRA中的issue key位置
        'header [data-testid*="breadcrumb"]'
      ];
      
      for (const selector of keySelectors) {
        const keyElement = document.querySelector(selector);
        if (keyElement) {
          const text = keyElement.textContent?.trim() || '';
          const match = text.match(/[A-Z]+-\d+/);
          if (match) {
            issueData.issueKey = match[0];
            break;
          }
        }
      }
      
      // 如果还没找到issue key，尝试从URL中提取
      if (!issueData.issueKey) {
        const urlMatch = window.location.href.match(/\/browse\/([A-Z]+-\d+)/);
        if (urlMatch && urlMatch[1]) {
          issueData.issueKey = urlMatch[1];
        }
      }
      
      // Extract summary
      const summarySelectors = [
        '[data-testid="issue.views.issue-base.foundation.summary.heading"] h1',
        '#summary-val',
        '.issue-header-summary',
        'h1[data-testid="issue.views.issue-base.foundation.summary.heading"]',
        '[data-test-id*="summary"]',
        '.issue-title',
        'h1.summary',
        '[aria-label*="summary"]',
        'header h1' // 新版JIRA中的标题位置
      ];
      
      for (const selector of summarySelectors) {
        const summaryElement = document.querySelector(selector);
        if (summaryElement) {
          issueData.summary = summaryElement.textContent?.trim() || '';
          break;
        }
      }
      
      // Extract status
      const statusSelectors = [
        '[data-testid="issue.views.issue-base.foundation.status.status-field-wrapper"]',
        '#status-val',
        '.issue-status',
        '[data-test-id*="status"]',
        '.status-field',
        '[aria-label*="status"]',
        '.status-indicator',
        '[data-testid*="status"]'
      ];
      
      for (const selector of statusSelectors) {
        const statusElement = document.querySelector(selector);
        if (statusElement) {
          issueData.status = statusElement.textContent?.trim() || '';
          break;
        }
      }
    } catch (error) {
      console.warn('Error extracting issue data from page:', error);
    }
    
    try {
      // Extract assignee
      const assigneeSelectors = [
        '[data-testid="issue.views.field.user.assignee"]',
        '#assignee-val',
        '.issue-assignee',
        '[data-test-id*="assignee"]',
        '[aria-label*="assignee"]',
        '.assignee-field',
        'img[alt*="Assignee"]',
        '[data-testid*="assignee"]'
      ];
      
      for (const selector of assigneeSelectors) {
        const assigneeElement = document.querySelector(selector);
        if (assigneeElement) {
          issueData.assignee = assigneeElement.textContent?.trim() || 
                              assigneeElement.getAttribute('title') || 
                              assigneeElement.getAttribute('alt')?.replace('Assignee:', '')?.trim() || '';
          break;
        }
      }
      
      // Extract priority
      const prioritySelectors = [
        '[data-testid="issue.views.field.priority"]',
        '#priority-val',
        '.issue-priority',
        '[data-test-id*="priority"]',
        '[aria-label*="priority"]',
        '.priority-field',
        'img[alt*="Priority"]',
        '[data-testid*="priority"]'
      ];
      
      for (const selector of prioritySelectors) {
        const priorityElement = document.querySelector(selector);
        if (priorityElement) {
          issueData.priority = priorityElement.textContent?.trim() || 
                              priorityElement.getAttribute('title') || 
                              priorityElement.getAttribute('alt')?.replace('Priority:', '')?.trim() || '';
          break;
        }
      }
      
      // Extract issue type
      const typeSelectors = [
        '[data-testid="issue.views.field.issuetype"]',
        '#type-val',
        '.issue-type',
        '[data-test-id*="issue-type"]',
        '[aria-label*="issue type"]',
        '.type-field',
        'img[alt*="Issue Type"]',
        '[data-testid*="issuetype"]'
      ];
      
      for (const selector of typeSelectors) {
        const typeElement = document.querySelector(selector);
        if (typeElement) {
          issueData.issueType = typeElement.textContent?.trim() || 
                               typeElement.getAttribute('title') || 
                               typeElement.getAttribute('alt')?.replace('Issue Type:', '')?.trim() || '';
          break;
        }
      }
      
      // Extract created date
      const createdSelectors = [
        '[data-testid="issue.views.field.created"]',
        '#created-val',
        '.issue-created',
        '[data-test-id*="created"]',
        '[aria-label*="created"]',
        '.created-field',
        '[data-testid*="created"]'
      ];
      
      for (const selector of createdSelectors) {
        const createdElement = document.querySelector(selector);
        if (createdElement) {
          issueData.created = createdElement.textContent?.trim() || '';
          break;
        }
      }
      
      // Extract updated date
      const updatedSelectors = [
        '[data-testid="issue.views.field.updated"]',
        '#updated-val',
        '.issue-updated',
        '[data-test-id*="updated"]',
        '[aria-label*="updated"]',
        '.updated-field',
        '[data-testid*="updated"]'
      ];
      
      for (const selector of updatedSelectors) {
        const updatedElement = document.querySelector(selector);
        if (updatedElement) {
          issueData.updated = updatedElement.textContent?.trim() || '';
          break;
        }
      }
      
      // Extract description
      const descriptionSelectors = [
        '[data-testid="issue.views.field.description"]',
        '#description-val',
        '.issue-description',
        '[data-test-id*="description"]',
        '[aria-label*="description"]',
        '.description-field',
        '[data-testid*="description"]'
      ];
      
      for (const selector of descriptionSelectors) {
        const descriptionElement = document.querySelector(selector);
        if (descriptionElement) {
          issueData.description = descriptionElement.textContent?.trim() || '';
          break;
        }
      }
    } catch (error) {
      console.warn('Error extracting additional issue data from page:', error);
    }
    
    return issueData;
  }
  
  // Show notification on page
  function showPageNotification(message, type = 'info') {
    // Remove existing notifications
    const existingNotifications = document.querySelectorAll('.dutyrobot-notification');
    existingNotifications.forEach(notification => notification.remove());
    
    // Create notification element
    const notification = document.createElement('div');
    notification.className = `dutyrobot-notification dutyrobot-notification-${type}`;
    notification.style.cssText = `
      position: fixed;
      top: 20px;
      right: 20px;
      background: ${type === 'success' ? '#28a745' : type === 'error' ? '#dc3545' : type === 'warning' ? '#ffc107' : '#17a2b8'};
      color: ${type === 'warning' ? '#212529' : 'white'};
      padding: 12px 20px;
      border-radius: 8px;
      font-size: 14px;
      font-weight: 500;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
      z-index: 10000;
      max-width: 300px;
      animation: slideIn 0.3s ease;
    `;
    
    notification.textContent = message;
    
    // Add animation styles
    const style = document.createElement('style');
    style.textContent = `
      @keyframes slideIn {
        from {
          transform: translateX(100%);
          opacity: 0;
        }
        to {
          transform: translateX(0);
          opacity: 1;
        }
      }
    `;
    document.head.appendChild(style);
    
    // Add notification to page
    document.body.appendChild(notification);
    
    // Auto remove after 3 seconds
    setTimeout(() => {
      if (notification.parentNode) {
        notification.style.animation = 'slideOut 0.3s ease';
        setTimeout(() => {
          if (notification.parentNode) {
            notification.remove();
          }
        }, 300);
      }
    }, 3000);
  }
  
  // Handle JIRA data extraction request from popup
  async function handleExtractJiraData(request, sendResponse) {
    try {
      console.log('Extracting JIRA data:', request.type);
      let extractedData = [];
      
      // 根据请求类型决定提取方式
      if (request.type === 'current') {
        // 提取当前页面数据
        if (isSingleIssuePage()) {
          extractedData = extractSingleIssueData();
        } else if (isIssueListPage()) {
          extractedData = extractIssueListData();
        } else {
          throw new Error('当前页面不包含可提取的JIRA数据');
        }
      } else if (request.type === 'filter') {
        // 提取筛选器页面数据
        if (isIssueListPage() && window.location.href.includes('/issues/?filter=')) {
          extractedData = extractIssueListData();
        } else {
          throw new Error('当前页面不是有效的JIRA筛选器页面');
        }
      } else if (request.type === 'selection') {
        // 提取选中的数据
        extractedData = extractSelectedIssues();
      }
      
      // 转换数据格式以适应popup中的显示
      const formattedData = extractedData.map(issue => ({
        key: issue.issueKey,
        summary: issue.summary,
        status: issue.status,
        assignee: issue.assignee,
        priority: issue.priority,
        type: issue.issueType
      }));
      
      // 显示成功通知
      if (formattedData.length > 0) {
        showPageNotification(`成功提取 ${formattedData.length} 条JIRA数据`, 'success');
        sendResponse({ success: true, data: formattedData });
      } else {
        throw new Error('未找到可提取的JIRA数据');
      }
    } catch (error) {
      console.error('提取JIRA数据失败:', error);
      showPageNotification(`提取失败: ${error.message}`, 'error');
      sendResponse({ success: false, error: error.message });
    }
  }
  
  // 在页面上注入提取按钮
  function injectExtractButton() {
    // 检查是否已经存在按钮
    if (document.querySelector('#dutyrobot-extract-button')) {
      return;
    }
    
    // 创建浮动按钮
    const button = document.createElement('button');
    button.id = 'dutyrobot-extract-button';
    button.innerHTML = '🤖 提取数据';
    button.style.cssText = `
      position: fixed;
      bottom: 20px;
      right: 20px;
      background: #0052cc;
      color: white;
      border: none;
      border-radius: 4px;
      padding: 8px 16px;
      font-size: 14px;
      font-weight: 500;
      cursor: pointer;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
      z-index: 9999;
      display: flex;
      align-items: center;
      gap: 6px;
    `;
    
    // 添加悬停效果
    button.addEventListener('mouseover', () => {
      button.style.background = '#0065ff';
    });
    
    button.addEventListener('mouseout', () => {
      button.style.background = '#0052cc';
    });
    
    // 添加点击事件
    button.addEventListener('click', async () => {
      try {
        // 显示加载状态
        button.innerHTML = '🔄 提取中...';
        button.disabled = true;
        
        // 提取数据
        let extractedData = [];
        if (isSingleIssuePage()) {
          extractedData = extractSingleIssueData();
        } else if (isIssueListPage()) {
          extractedData = extractIssueListData();
        } else {
          throw new Error('当前页面不包含可提取的JIRA数据');
        }
        
        // 转换数据格式
        const formattedData = extractedData.map(issue => ({
          key: issue.issueKey,
          summary: issue.summary,
          status: issue.status,
          assignee: issue.assignee,
          priority: issue.priority,
          type: issue.issueType
        }));
        
        // 发送数据到扩展
        if (formattedData.length > 0) {
          await chrome.runtime.sendMessage({
            action: 'jiraDataExtracted',
            data: formattedData,
            source: 'button',
            pageType: getPageType(),
            timestamp: new Date().toISOString()
          });
          
          // 显示成功通知
          showPageNotification(`成功提取 ${formattedData.length} 条JIRA数据`, 'success');
        } else {
          throw new Error('未找到可提取的JIRA数据');
        }
      } catch (error) {
        console.error('提取数据失败:', error);
        showPageNotification(`提取失败: ${error.message}`, 'error');
      } finally {
        // 恢复按钮状态
        button.innerHTML = '🤖 提取数据';
        button.disabled = false;
      }
    });
    
    // 添加到页面
    document.body.appendChild(button);
  }
  
  // Initialize content script
  function init() {
    console.log('DutyRobot content script initialized on:', window.location.href);
    
    // 检查是否是JIRA页面
    if (isJiraPage()) {
      // 注入提取按钮
      injectExtractButton();
      
      // 发送页面信息到background script
      chrome.runtime.sendMessage({
        action: 'pageLoaded',
        pageInfo: {
          isJiraPage: true,
          pageType: getPageType(),
          issueCount: getIssueCount(),
          url: window.location.href
        }
      }).catch(error => {
        console.log('Background script not available:', error);
      });
    }
  }
  
  // Initialize when DOM is ready
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', init);
  } else {
    init();
  }
  
  // Handle page navigation in SPAs
  let lastUrl = window.location.href;
  const observer = new MutationObserver(() => {
    // Check if URL has changed (SPA navigation)
    if (window.location.href !== lastUrl) {
      lastUrl = window.location.href;
      
      // Re-check if this is a JIRA page after navigation
      if (isJiraPage()) {
        console.log('JIRA SPA navigation detected:', window.location.href);
        
        // 重新注入提取按钮
        injectExtractButton();
        
        chrome.runtime.sendMessage({
          action: 'pageLoaded',
          pageInfo: {
            isJiraPage: true,
            pageType: getPageType(),
            issueCount: getIssueCount(),
            url: window.location.href
          }
        }).catch(error => {
          console.log('Background script not available:', error);
        });
      } else {
        setTimeout(init, 1000); // Delay to allow page to load
      }
    }
    
    // 检查DOM变化，可能需要重新注入按钮
    // 这处理了JIRA页面动态加载内容的情况
    if (isJiraPage() && !document.querySelector('#dutyrobot-extract-button')) {
      injectExtractButton();
    }
  });
  
  observer.observe(document.body, {
    childList: true,
    subtree: true
  });
  
})();