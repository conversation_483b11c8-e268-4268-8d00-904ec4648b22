/**
 * reminderService.js
 * 基于任务的提醒系统，使用Chrome Alarms API实现定时提醒功能
 */

import { notificationService } from '../../utils/notifications';
import taskManager from '../../utils/taskManager';
import { TASK_STATUS } from '../../utils/taskManager';

// 提醒类型常量
const REMINDER_TYPES = {
  DUE_SOON: 'due_soon',     // 任务即将到期
  OVERDUE: 'overdue',       // 任务已逾期
  CUSTOM: 'custom'          // 自定义提醒
};

// 默认提醒配置
const DEFAULT_REMINDER_CONFIG = {
  enabled: true,            // 是否启用提醒
  dueSoonThreshold: 24,     // 即将到期阈值（小时）
  checkInterval: 30,        // 检查间隔（分钟）
  overdueCheckInterval: 120, // 逾期检查间隔（分钟）
  quietHours: {             // 免打扰时间
    enabled: false,
    start: '22:00',
    end: '08:00'
  }
};

// 提醒服务主要警报名称
const MAIN_ALARM_NAME = 'dutyrobot-reminder-check';

/**
 * 提醒服务类
 */
class ReminderService {
  constructor() {
    this.config = DEFAULT_REMINDER_CONFIG;
    this.customReminders = new Map(); // 存储自定义提醒
    
    // 初始化事件监听
    this._setupEventListeners();
  }
  
  /**
   * 初始化提醒服务
   * @param {Object} config - 提醒配置
   */
  async initialize(config = {}) {
    // 合并配置
    this.config = {
      ...DEFAULT_REMINDER_CONFIG,
      ...config
    };
    
    // 如果启用了提醒，则设置主要警报
    if (this.config.enabled) {
      await this.setupMainAlarm();
    }
    
    console.log('ReminderService initialized with config:', this.config);
  }
  
  /**
   * 设置事件监听器
   * @private
   */
  _setupEventListeners() {
    // 监听警报触发事件
    chrome.alarms.onAlarm.addListener(this._handleAlarm.bind(this));
  }
  
  /**
   * 处理警报触发
   * @param {Object} alarm - 触发的警报对象
   * @private
   */
  async _handleAlarm(alarm) {
    console.log('Alarm triggered:', alarm.name);
    
    // 处理主要检查警报
    if (alarm.name === MAIN_ALARM_NAME) {
      await this._checkTaskReminders();
      return;
    }
    
    // 处理自定义提醒警报
    if (alarm.name.startsWith('custom-reminder-')) {
      const reminderId = alarm.name.replace('custom-reminder-', '');
      await this._triggerCustomReminder(reminderId);
      return;
    }
  }
  
  /**
   * 设置主要警报（定期检查任务提醒）
   */
  async setupMainAlarm() {
    // 先清除现有的主要警报
    await this.clearMainAlarm();
    
    // 创建新的主要警报
    chrome.alarms.create(MAIN_ALARM_NAME, {
      periodInMinutes: this.config.checkInterval
    });
    
    console.log(`Main reminder alarm set to check every ${this.config.checkInterval} minutes`);
  }
  
  /**
   * 清除主要警报
   */
  async clearMainAlarm() {
    return new Promise((resolve) => {
      chrome.alarms.clear(MAIN_ALARM_NAME, (wasCleared) => {
        resolve(wasCleared);
      });
    });
  }
  
  /**
   * 检查任务提醒
   * @private
   */
  async _checkTaskReminders() {
    // 如果在免打扰时间内，则跳过检查
    if (this._isInQuietHours()) {
      console.log('Skipping reminder check during quiet hours');
      return;
    }
    
    // 检查即将到期的任务
    await this._checkDueSoonTasks();
    
    // 检查已逾期的任务
    await this._checkOverdueTasks();
  }
  
  /**
   * 检查是否在免打扰时间内
   * @returns {boolean} 是否在免打扰时间内
   * @private
   */
  _isInQuietHours() {
    const { quietHours } = this.config;
    
    // 如果未启用免打扰，则返回false
    if (!quietHours || !quietHours.enabled) {
      return false;
    }
    
    const now = new Date();
    const currentHour = now.getHours();
    const currentMinute = now.getMinutes();
    const currentTime = currentHour * 60 + currentMinute; // 转换为分钟计数
    
    // 解析免打扰开始和结束时间
    const [startHour, startMinute] = quietHours.start.split(':').map(Number);
    const [endHour, endMinute] = quietHours.end.split(':').map(Number);
    
    const startTime = startHour * 60 + startMinute;
    const endTime = endHour * 60 + endMinute;
    
    // 处理跨天的情况
    if (startTime > endTime) {
      // 例如 22:00 - 08:00
      return currentTime >= startTime || currentTime <= endTime;
    } else {
      // 例如 08:00 - 22:00
      return currentTime >= startTime && currentTime <= endTime;
    }
  }
  
  /**
   * 检查即将到期的任务
   * @private
   */
  async _checkDueSoonTasks() {
    try {
      // 计算阈值时间（小时）
      const thresholdHours = this.config.dueSoonThreshold;
      const thresholdDays = thresholdHours / 24;
      
      // 获取即将到期的任务
      const dueSoonTasks = await taskManager.getUpcomingTasks(thresholdDays);
      
      if (dueSoonTasks.length > 0) {
        console.log(`Found ${dueSoonTasks.length} tasks due soon`);
        
        // 为每个即将到期的任务创建通知
        for (const task of dueSoonTasks) {
          // 计算剩余时间
          const dueDate = new Date(task.dueDate);
          const now = new Date();
          const hoursLeft = Math.round((dueDate - now) / (1000 * 60 * 60));
          
          // 创建通知
          await notificationService.createTextNotification(
            '任务即将到期',
            `${task.title} 将在 ${hoursLeft} 小时后到期`,
            {
              requireInteraction: true,
              relatedTaskId: task.id,
              buttons: [
                { title: '查看任务' },
                { title: '标记为完成' }
              ]
            },
            () => {
              // 点击通知时的回调
              chrome.runtime.sendMessage({
                action: 'openTask',
                taskId: task.id
              });
            }
          );
        }
      }
    } catch (error) {
      console.error('Error checking due soon tasks:', error);
    }
  }
  
  /**
   * 检查已逾期的任务
   * @private
   */
  async _checkOverdueTasks() {
    try {
      // 获取已逾期的任务
      const overdueTasks = await taskManager.getOverdueTasks();
      
      if (overdueTasks.length > 0) {
        console.log(`Found ${overdueTasks.length} overdue tasks`);
        
        // 为每个逾期的任务创建通知
        for (const task of overdueTasks) {
          // 计算逾期时间
          const dueDate = new Date(task.dueDate);
          const now = new Date();
          const daysOverdue = Math.round((now - dueDate) / (1000 * 60 * 60 * 24));
          
          // 创建通知
          await notificationService.createTextNotification(
            '任务已逾期',
            `${task.title} 已逾期 ${daysOverdue} 天`,
            {
              requireInteraction: true,
              relatedTaskId: task.id,
              buttons: [
                { title: '查看任务' },
                { title: '标记为完成' }
              ]
            },
            () => {
              // 点击通知时的回调
              chrome.runtime.sendMessage({
                action: 'openTask',
                taskId: task.id
              });
            }
          );
        }
      }
    } catch (error) {
      console.error('Error checking overdue tasks:', error);
    }
  }
  
  /**
   * 创建自定义提醒
   * @param {string} taskId - 任务ID
   * @param {Date|string} reminderTime - 提醒时间
   * @param {string} message - 提醒消息
   * @returns {string} 提醒ID
   */
  async createCustomReminder(taskId, reminderTime, message) {
    try {
      // 获取任务信息
      const task = await taskManager.getTask(taskId);
      if (!task) {
        throw new Error(`Task not found: ${taskId}`);
      }
      
      // 解析提醒时间
      let reminderDate;
      if (reminderTime instanceof Date) {
        reminderDate = reminderTime;
      } else {
        reminderDate = new Date(reminderTime);
      }
      
      // 生成提醒ID
      const reminderId = `${taskId}-${Date.now()}`;
      
      // 存储自定义提醒信息
      this.customReminders.set(reminderId, {
        taskId,
        message: message || `提醒：${task.title}`,
        time: reminderDate
      });
      
      // 计算提醒时间（分钟）
      const now = new Date();
      const delayInMinutes = Math.max(0, (reminderDate - now) / (1000 * 60));
      
      // 创建警报
      chrome.alarms.create(`custom-reminder-${reminderId}`, {
        when: reminderDate.getTime()
      });
      
      console.log(`Custom reminder created for task ${taskId}, will trigger in ${delayInMinutes.toFixed(1)} minutes`);
      
      return reminderId;
    } catch (error) {
      console.error('Error creating custom reminder:', error);
      throw error;
    }
  }
  
  /**
   * 触发自定义提醒
   * @param {string} reminderId - 提醒ID
   * @private
   */
  async _triggerCustomReminder(reminderId) {
    try {
      // 获取提醒信息
      const reminder = this.customReminders.get(reminderId);
      if (!reminder) {
        console.warn(`Custom reminder not found: ${reminderId}`);
        return;
      }
      
      // 获取任务信息
      const task = await taskManager.getTask(reminder.taskId);
      if (!task) {
        console.warn(`Task not found for reminder: ${reminder.taskId}`);
        return;
      }
      
      // 如果任务已完成或取消，则不发送提醒
      if (task.status === TASK_STATUS.DONE || task.status === TASK_STATUS.CANCELLED) {
        console.log(`Skipping reminder for completed/cancelled task: ${task.id}`);
        return;
      }
      
      // 创建通知
      await notificationService.createTextNotification(
        '任务提醒',
        reminder.message,
        {
          requireInteraction: true,
          relatedTaskId: task.id,
          buttons: [
            { title: '查看任务' },
            { title: '稍后提醒' }
          ]
        },
        () => {
          // 点击通知时的回调
          chrome.runtime.sendMessage({
            action: 'openTask',
            taskId: task.id
          });
        }
      );
      
      // 移除已触发的提醒
      this.customReminders.delete(reminderId);
    } catch (error) {
      console.error('Error triggering custom reminder:', error);
    }
  }
  
  /**
   * 取消自定义提醒
   * @param {string} reminderId - 提醒ID
   * @returns {Promise<boolean>} 是否成功取消
   */
  async cancelCustomReminder(reminderId) {
    return new Promise((resolve) => {
      // 清除警报
      chrome.alarms.clear(`custom-reminder-${reminderId}`, (wasCleared) => {
        if (wasCleared) {
          // 移除提醒信息
          this.customReminders.delete(reminderId);
        }
        resolve(wasCleared);
      });
    });
  }
  
  /**
   * 暂停所有提醒
   * @returns {Promise<boolean>} 是否成功暂停
   */
  async pauseReminders() {
    // 更新配置
    this.config.enabled = false;
    
    // 清除主要警报
    return await this.clearMainAlarm();
  }
  
  /**
   * 恢复所有提醒
   * @returns {Promise<boolean>} 是否成功恢复
   */
  async resumeReminders() {
    // 更新配置
    this.config.enabled = true;
    
    // 重新设置主要警报
    await this.setupMainAlarm();
    return true;
  }
  
  /**
   * 更新提醒配置
   * @param {Object} newConfig - 新配置
   */
  async updateConfig(newConfig) {
    // 合并配置
    this.config = {
      ...this.config,
      ...newConfig
    };
    
    // 如果启用状态改变，则更新警报
    if (newConfig.enabled !== undefined) {
      if (newConfig.enabled) {
        await this.setupMainAlarm();
      } else {
        await this.clearMainAlarm();
      }
    } else if (newConfig.checkInterval !== undefined && this.config.enabled) {
      // 如果检查间隔改变，则重新设置警报
      await this.setupMainAlarm();
    }
    
    console.log('Reminder config updated:', this.config);
  }
}

// 创建并导出提醒服务实例
const reminderService = new ReminderService();
export default reminderService;

// 导出常量
export {
  REMINDER_TYPES,
  DEFAULT_REMINDER_CONFIG
};