/**
 * background.js
 * Chrome Extension Background Script (Service Worker)
 */

// Import services
import reminderService from './services/reminderService.js';
import * as storage from '../utils/storage.js';
import * as taskManager from '../utils/taskManager.js';
import { showNotification } from '../utils/notifications.js';

// Extension installation handler
chrome.runtime.onInstalled.addListener(async () => {
  console.log('DutyRobot Extension installed');
  
  // Initialize default settings if not exists
  chrome.storage.sync.get(['dutyRobotSettings'], async (result) => {
    if (!result.dutyRobotSettings) {
      chrome.storage.sync.set({
        dutyRobotSettings: {
          showNotifications: true,
          jiraUrl: 'https://jira.dotfashion.cn',
          gitlabUrl: 'https://gitlab.sheincorp.cn',
          region: 'cn',
          reminders: {
            enabled: true,
            dueSoonThreshold: 24,
            checkInterval: 30,
            overdueCheckInterval: 120,
            quietHours: {
              enabled: false,
              start: '22:00',
              end: '08:00'
            }
          }
        }
      });
    }
    
    // Initialize reminder service
    const settings = result.dutyRobotSettings || {};
    await reminderService.initialize(settings.reminders);
    
    // Create context menus
    chrome.contextMenus.create({
      id: 'dutyrobot-extract',
      title: 'Extract JIRA Data',
      contexts: ['page'],
      documentUrlPatterns: [
        'https://jira.dotfashion.cn/*'
      ]
    });
    
    chrome.contextMenus.create({
      id: 'dutyrobot-extract-selection',
      title: 'Extract Selected JIRA Issues',
      contexts: ['selection'],
      documentUrlPatterns: [
        'https://jira.dotfashion.cn/*'
      ]
    });
  });
});

// Context menu click handler
chrome.contextMenus.onClicked.addListener((info, tab) => {
  console.log('Context menu clicked:', info.menuItemId);
  
  switch (info.menuItemId) {
    case 'dutyrobot-extract':
      extractDataFromTab(tab, 'current');
      break;
      
    case 'dutyrobot-extract-selection':
      extractDataFromTab(tab, 'selection');
      break;
  }
});

// Extract data from tab
function extractDataFromTab(tab, extractType) {
  chrome.tabs.sendMessage(tab.id, {
    action: 'extractData',
    extractType: extractType
  }, (response) => {
    if (chrome.runtime.lastError) {
      console.error('Error sending message to content script:', chrome.runtime.lastError);
      showNotification('Error', 'Failed to extract data from page');
    } else if (response && response.success) {
      const count = response.data ? response.data.length : 0;
      showNotification('Success', `Extracted ${count} JIRA issues`);
    } else {
      showNotification('Error', response?.error || 'Failed to extract data');
    }
  });
}

// Note: showNotification function is imported from ../utils/notifications.js

// Handle extension updates
chrome.runtime.onUpdateAvailable.addListener(() => {
  console.log('Extension update available');
  // Optionally auto-reload the extension
  // chrome.runtime.reload();
});

// Handle extension startup
chrome.runtime.onStartup.addListener(() => {
  console.log('DutyRobot Extension started');
});

// Error handling for unhandled promise rejections
self.addEventListener('unhandledrejection', (event) => {
  console.error('Unhandled promise rejection in background script:', event.reason);
});

// Handle runtime messages
chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
  console.log('Background received message:', message);
  
  // Route messages to appropriate handlers
  handleMessage(message, sender, sendResponse);
  return true; // Keep the message channel open for async response
});

/**
 * Generic message handler to route messages based on action.
 * @param {Object} message - The message object
 * @param {Object} sender - The sender information
 * @param {Function} sendResponse - The response callback
 */
async function handleMessage(message, sender, sendResponse) {
  try {
    if (message.action && message.action.startsWith('reminder-')) {
      await handleReminderMessages(message, sender, sendResponse);
    } else if (message.action && message.action.startsWith('task-')) {
      await handleTaskMessages(message, sender, sendResponse);
    } else if (message.action && message.action.startsWith('settings-')) {
      await handleSettingsMessages(message, sender, sendResponse);
    } else if (message.action === 'openTask') {
      chrome.action.openPopup();
      sendResponse({ success: true });
    } else {
      console.warn('Unknown message action:', message.action);
      sendResponse({ success: false, error: 'Unknown action' });
    }
  } catch (error) {
    console.error(`Error handling message for action ${message.action}:`, error);
    // Log error to storage for later review
    logError({ action: message.action, error: error.message, stack: error.stack });
    sendResponse({ success: false, error: error.message });
  }
}

/**
 * Handle reminder-related messages
 * @param {Object} message - The message object
 * @param {Object} sender - The sender information
 * @param {Function} sendResponse - The response callback
 */
async function handleReminderMessages(message, sender, sendResponse) {
  switch (message.action) {
    case 'reminder-create':
      const reminderId = await reminderService.createCustomReminder(
        message.taskId,
        message.reminderTime,
        message.message
      );
      sendResponse({ success: true, reminderId });
      break;
    case 'reminder-cancel':
      const cancelled = await reminderService.cancelCustomReminder(message.reminderId);
      sendResponse({ success: cancelled });
      break;
    case 'reminder-pause':
      const paused = await reminderService.pauseReminders();
      sendResponse({ success: paused });
      break;
    case 'reminder-resume':
      const resumed = await reminderService.resumeReminders();
      sendResponse({ success: resumed });
      break;
    case 'reminder-update-config':
      await reminderService.updateConfig(message.config);
      sendResponse({ success: true });
      break;
    default:
      console.warn('Unknown reminder action:', message.action);
      sendResponse({ success: false, error: 'Unknown action' });
  }
}

/**
 * Handle task-related messages
 * @param {Object} message - The message object
 * @param {Object} sender - The sender information
 * @param {Function} sendResponse - The response callback
 */
async function handleTaskMessages(message, sender, sendResponse) {
  switch (message.action) {
    case 'task-create':
      const newTask = await taskManager.createTask(message.task);
      sendResponse({ success: true, task: newTask });
      break;
    case 'task-get-all':
      const tasks = await taskManager.getAllTasks();
      sendResponse({ success: true, tasks });
      break;
    case 'task-get-by-id':
      const task = await taskManager.getTaskById(message.taskId);
      sendResponse({ success: true, task });
      break;
    case 'task-update':
      const updatedTask = await taskManager.updateTask(message.taskId, message.updates);
      sendResponse({ success: true, task: updatedTask });
      break;
    case 'task-delete':
      const deleted = await taskManager.deleteTask(message.taskId);
      sendResponse({ success: deleted });
      break;
    case 'task-search':
      const searchResults = await taskManager.searchTasks(message.query);
      sendResponse({ success: true, tasks: searchResults });
      break;
    case 'task-filter-by-status':
      const filteredTasks = await taskManager.filterTasksByStatus(message.status);
      sendResponse({ success: true, tasks: filteredTasks });
      break;
    default:
      console.warn('Unknown task action:', message.action);
      sendResponse({ success: false, error: 'Unknown action' });
  }
}

/**
 * Handle settings-related messages
 * @param {Object} message - The message object
 * @param {Object} sender - The sender information
 * @param {Function} sendResponse - The response callback
 */
async function handleSettingsMessages(message, sender, sendResponse) {
  switch (message.action) {
    case 'settings-get':
      const settings = await storage.getSettings();
      sendResponse({ success: true, settings });
      break;
    case 'settings-update':
      await storage.updateSettings(message.updates);
      sendResponse({ success: true });
      break;
    default:
      console.warn('Unknown settings action:', message.action);
      sendResponse({ success: false, error: 'Unknown action' });
  }
}

/**
 * Logs an error to storage.
 * @param {Object} errorInfo - Information about the error.
 */
async function logError(errorInfo) {
  try {
    let errors = await storage.get('errorLogs') || [];
    errors.push({ ...errorInfo, timestamp: Date.now() });
    // Keep only the last 100 errors to prevent excessive storage usage
    if (errors.length > 100) {
      errors = errors.slice(errors.length - 100);
    }
    await storage.set('errorLogs', errors);
  } catch (e) {
    console.error('Failed to log error to storage:', e);
  }
}