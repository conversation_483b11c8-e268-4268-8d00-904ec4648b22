import React, { useState, useEffect } from 'react';
import { settingsStorage } from '../utils/storage';

const OptionsApp = () => {
  const [settings, setSettings] = useState({
    region: 'cn',
    jira: {
      url: '',
      username: '',
      apiToken: ''
    },
    gitlab: {
      url: 'https://gitlab.sheincorp.cn/api/v4',
      token: '',
      projectId: ''
    },
    notifications: {
      enabled: true,
      reminderInterval: 30, // 分钟
      soundEnabled: true
    },
    extractFields: {
      issueKey: true,
      summary: true,
      status: true,
      assignee: true,
      priority: true,
      issueType: true,
      created: false,
      updated: false,
      description: false,
      comments: false
    },
    maxExtractCount: 100,
    defaultExportFormat: 'csv'
  });
  
  const [showToken, setShowToken] = useState(false);
  const [showJiraToken, setShowJiraToken] = useState(false);
  const [saveStatus, setSaveStatus] = useState('');
  const [connectionStatus, setConnectionStatus] = useState('');
  const [jiraConnectionStatus, setJiraConnectionStatus] = useState('');
  const [testing, setTesting] = useState(false);
  const [testingJira, setTestingJira] = useState(false);

  useEffect(() => {
    loadSettings();
  }, []);

  const loadSettings = async () => {
    try {
      const storedSettings = await settingsStorage.getSettings();
      setSettings(prev => ({ ...prev, ...storedSettings }));
    } catch (error) {
      console.error('加载设置失败:', error);
    }
  };

  const saveSettings = async () => {
    try {
      await settingsStorage.updateSettings(settings);
      setSaveStatus('✅ 设置已保存');
      setTimeout(() => setSaveStatus(''), 3000);
    } catch (error) {
      setSaveStatus('❌ 保存失败: ' + error.message);
      console.error('保存设置失败:', error);
    }
  };

  const testGitlabConnection = async () => {
    if (!settings.gitlab.token) {
      setConnectionStatus('请先输入GitLab访问令牌');
      return;
    }

    setTesting(true);
    setConnectionStatus('测试连接中...');

    try {
      const response = await chrome.runtime.sendMessage({
        action: 'fetchGitlabData',
        projectId: settings.gitlab.projectId || 10100,
        endpoint: 'repository/branches'
      });

      if (response.success) {
        setConnectionStatus('✅ 连接成功！GitLab API可正常访问');
      } else {
        setConnectionStatus('❌ 连接失败: ' + response.error);
      }
    } catch (error) {
      setConnectionStatus('❌ 测试连接时出错: ' + error.message);
    } finally {
      setTesting(false);
    }
  };

  const testJiraConnection = async () => {
    if (!settings.jira.url || !settings.jira.username || !settings.jira.apiToken) {
      setJiraConnectionStatus('请先填写完整的JIRA配置信息');
      return;
    }

    setTestingJira(true);
    setJiraConnectionStatus('测试JIRA连接中...');

    try {
      const response = await chrome.runtime.sendMessage({
        action: 'testJiraConnection',
        jiraConfig: settings.jira
      });

      if (response.success) {
        setJiraConnectionStatus('✅ 连接成功！JIRA API可正常访问');
      } else {
        setJiraConnectionStatus('❌ 连接失败: ' + response.error);
      }
    } catch (error) {
      setJiraConnectionStatus('❌ 测试连接时出错: ' + error.message);
    } finally {
      setTestingJira(false);
    }
  };

  const updateSetting = (key, value) => {
    setSettings(prev => ({
      ...prev,
      [key]: value
    }));
  };

  const updateNestedSetting = (parentKey, key, value) => {
    setSettings(prev => ({
      ...prev,
      [parentKey]: {
        ...prev[parentKey],
        [key]: value
      }
    }));
  };

  const updateExtractField = (field, checked) => {
    setSettings(prev => ({
      ...prev,
      extractFields: {
        ...prev.extractFields,
        [field]: checked
      }
    }));
  };

  return (
    <div className="container">
      <header className="header">
        <h1>🤖 值班萝卜 - 高级选项</h1>
        <p className="subtitle">配置扩展的详细设置和高级功能</p>
      </header>

      <main className="main-content">
        <div className="settings-grid">
          {/* JIRA配置 */}
          <section className="settings-section">
            <h2>JIRA配置</h2>

            <div className="form-group">
              <label htmlFor="jira-url">JIRA服务器地址</label>
              <input 
                type="url" 
                id="jira-url" 
                className="form-control"
                value={settings.jira.url}
                onChange={(e) => updateNestedSetting('jira', 'url', e.target.value)}
                placeholder="https://jira.example.com"
              />
              <small className="form-text">JIRA服务器的基础地址</small>
            </div>

            <div className="form-group">
              <label htmlFor="jira-username">JIRA用户名</label>
              <input 
                type="text" 
                id="jira-username" 
                className="form-control"
                value={settings.jira.username}
                onChange={(e) => updateNestedSetting('jira', 'username', e.target.value)}
                placeholder="<EMAIL>"
              />
              <small className="form-text">JIRA账号的用户名或邮箱</small>
            </div>

            <div className="form-group">
              <label htmlFor="jira-token">JIRA API令牌</label>
              <div className="input-group">
                <input 
                  type={showJiraToken ? 'text' : 'password'}
                  id="jira-token" 
                  className="form-control"
                  placeholder="输入JIRA API令牌"
                  value={settings.jira.apiToken}
                  onChange={(e) => updateNestedSetting('jira', 'apiToken', e.target.value)}
                />
                <button 
                  type="button" 
                  className="btn btn-secondary"
                  onClick={() => setShowJiraToken(!showJiraToken)}
                >
                  <span className="btn-icon">{showJiraToken ? '🙈' : '👁️'}</span>
                </button>
              </div>
              <small className="form-text">
                在JIRA账号设置中创建API令牌
              </small>
            </div>

            <div className="form-actions">
              <button className="btn btn-primary" onClick={saveSettings}>
                保存设置
              </button>
              <button 
                className="btn btn-secondary" 
                onClick={testJiraConnection}
                disabled={testingJira || !settings.jira.url || !settings.jira.apiToken}
              >
                {testingJira ? '测试中...' : '测试JIRA连接'}
              </button>
            </div>

            {jiraConnectionStatus && (
              <div className={`status-message ${
                jiraConnectionStatus.includes('✅') ? 'success' : 
                jiraConnectionStatus.includes('❌') ? 'error' : 'info'
              }`}>
                {jiraConnectionStatus}
              </div>
            )}
          </section>

          {/* GitLab API配置 */}
          <section className="settings-section">
            <h2>GitLab API配置</h2>

            <div className="form-group">
              <label htmlFor="gitlab-api-url">GitLab API地址</label>
              <input 
                type="url" 
                id="gitlab-api-url" 
                className="form-control"
                value={settings.gitlab.url}
                onChange={(e) => updateNestedSetting('gitlab', 'url', e.target.value)}
                placeholder="https://gitlab.sheincorp.cn/api/v4"
              />
              <small className="form-text">GitLab服务器的API基础地址</small>
            </div>

            <div className="form-group">
              <label htmlFor="gitlab-token">GitLab访问令牌</label>
              <div className="input-group">
                <input 
                  type={showToken ? 'text' : 'password'}
                  id="gitlab-token" 
                  className="form-control"
                  placeholder="输入GitLab个人访问令牌"
                  value={settings.gitlab.token}
                  onChange={(e) => updateNestedSetting('gitlab', 'token', e.target.value)}
                />
                <button 
                  type="button" 
                  className="btn btn-secondary"
                  onClick={() => setShowToken(!showToken)}
                >
                  <span className="btn-icon">{showToken ? '🙈' : '👁️'}</span>
                </button>
              </div>
              <small className="form-text">
                在GitLab中：Preferences → Access Tokens → 创建令牌
                <a href="https://gitlab.sheincorp.cn/-/profile/personal_access_tokens" target="_blank" rel="noopener noreferrer">
                  前往设置
                </a>
              </small>
            </div>

            <div className="form-group">
              <label htmlFor="gitlab-project-id">GitLab项目ID</label>
              <input 
                type="text" 
                id="gitlab-project-id" 
                className="form-control"
                value={settings.gitlab.projectId}
                onChange={(e) => updateNestedSetting('gitlab', 'projectId', e.target.value)}
                placeholder="例如：10100"
              />
              <small className="form-text">GitLab项目的ID，可在项目页面找到</small>
            </div>

            <div className="form-group">
              <label htmlFor="region">区域设置</label>
              <select 
                id="region" 
                className="form-control"
                value={settings.region}
                onChange={(e) => updateSetting('region', e.target.value)}
              >
                <option value="cn">中国区 (CN)</option>
                <option value="wi">WI区</option>
                <option value="overseas">海外区</option>
              </select>
              <small className="form-text">选择对应的GitLab项目区域</small>
            </div>

            <div className="form-actions">
              <button className="btn btn-primary" onClick={saveSettings}>
                保存设置
              </button>
              <button 
                className="btn btn-secondary" 
                onClick={testGitlabConnection}
                disabled={testing || !settings.gitlab.token}
              >
                {testing ? '测试中...' : '测试GitLab连接'}
              </button>
            </div>

            {connectionStatus && (
              <div className={`status-message ${
                connectionStatus.includes('✅') ? 'success' : 
                connectionStatus.includes('❌') ? 'error' : 'info'
              }`}>
                {connectionStatus}
              </div>
            )}
          </section>

          {/* 通知设置 */}
          <section className="settings-section">
            <h2>通知设置</h2>

            <div className="form-group">
              <label className="checkbox-label">
                <input 
                  type="checkbox" 
                  checked={settings.notifications.enabled}
                  onChange={(e) => updateNestedSetting('notifications', 'enabled', e.target.checked)}
                />
                <span>启用通知</span>
              </label>
              <small className="form-text">开启或关闭所有通知</small>
            </div>

            <div className="form-group">
              <label htmlFor="reminder-interval">提醒间隔（分钟）</label>
              <input 
                type="number" 
                id="reminder-interval" 
                className="form-control"
                value={settings.notifications.reminderInterval}
                onChange={(e) => updateNestedSetting('notifications', 'reminderInterval', parseInt(e.target.value))}
                min="1"
                max="120"
              />
              <small className="form-text">任务提醒的时间间隔</small>
            </div>

            <div className="form-group">
              <label className="checkbox-label">
                <input 
                  type="checkbox" 
                  checked={settings.notifications.soundEnabled}
                  onChange={(e) => updateNestedSetting('notifications', 'soundEnabled', e.target.checked)}
                />
                <span>启用提示音</span>
              </label>
              <small className="form-text">通知时播放提示音</small>
            </div>
          </section>

          {/* 数据提取设置 */}
          <section className="settings-section">
            <h2>数据提取设置</h2>

            <div className="form-group">
              <label>默认提取字段</label>
              <div className="checkbox-grid">
                {[
                  { key: 'issueKey', label: '问题编号' },
                  { key: 'summary', label: '标题' },
                  { key: 'status', label: '状态' },
                  { key: 'assignee', label: '经办人' },
                  { key: 'priority', label: '优先级' },
                  { key: 'issueType', label: '问题类型' },
                  { key: 'created', label: '创建时间' },
                  { key: 'updated', label: '更新时间' },
                  { key: 'description', label: '描述' },
                  { key: 'comments', label: '评论' }
                ].map(field => (
                  <label key={field.key} className="checkbox-label">
                    <input 
                      type="checkbox" 
                      checked={settings.extractFields[field.key]}
                      onChange={(e) => updateExtractField(field.key, e.target.checked)}
                    />
                    <span>{field.label}</span>
                  </label>
                ))}
              </div>
            </div>

            <div className="form-group">
              <label htmlFor="max-extract-count">最大提取数量</label>
              <input 
                type="number" 
                id="max-extract-count" 
                className="form-control"
                value={settings.maxExtractCount}
                onChange={(e) => updateSetting('maxExtractCount', parseInt(e.target.value))}
                min="1"
                max="1000"
              />
              <small className="form-text">单次提取的最大问题数量</small>
            </div>

            <div className="form-group">
              <label htmlFor="default-export-format">默认导出格式</label>
              <select 
                id="default-export-format" 
                className="form-control"
                value={settings.defaultExportFormat}
                onChange={(e) => updateSetting('defaultExportFormat', e.target.value)}
              >
                <option value="csv">CSV</option>
                <option value="json">JSON</option>
                <option value="excel">Excel</option>
              </select>
            </div>
          </section>
        </div>

        {saveStatus && (
          <div className={`save-status ${
            saveStatus.includes('✅') ? 'success' : 'error'
          }`}>
            {saveStatus}
          </div>
        )}
      </main>
    </div>
  );
};

export default OptionsApp;