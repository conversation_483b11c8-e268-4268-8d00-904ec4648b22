// DutyRobot Utility Functions

// Import GitLab service
import { gitlabService } from './gitlab';

// Storage utilities
export const storage = {
  // Get settings from Chrome storage
  async getSettings() {
    return new Promise((resolve) => {
      chrome.storage.sync.get(['dutyRobotSettings'], (result) => {
        const defaultSettings = {
          region: 'cn',
          gitlabApiUrl: '',
          gitlabAccessToken: '',
          extractFields: {
            issueKey: true,
            summary: true,
            status: true,
            assignee: true,
            priority: true,
            issueType: true,
            created: true,
            updated: true,
            description: false,
            comments: false
          },
          maxExtractCount: 100,
          defaultExportFormat: 'csv',
          showNotifications: true,
          projectConfigs: []
        };
        
        resolve(result.dutyRobotSettings || defaultSettings);
      });
    });
  },
  
  // Save settings to Chrome storage
  async saveSettings(settings) {
    return new Promise((resolve, reject) => {
      chrome.storage.sync.set({ dutyRobotSettings: settings }, () => {
        if (chrome.runtime.lastError) {
          reject(new Error(chrome.runtime.lastError.message));
        } else {
          resolve();
        }
      });
    });
  }
};

// Data formatting utilities
export const dataFormatter = {
  // Convert data to CSV format
  toCSV(data, fields = null) {
    if (!data || data.length === 0) {
      return '';
    }
    
    const headers = fields || Object.keys(data[0]);
    const csvHeaders = headers.join(',');
    
    const csvRows = data.map(row => {
      return headers.map(header => {
        const value = row[header] || '';
        // Escape quotes and wrap in quotes if contains comma, quote, or newline
        if (typeof value === 'string' && (value.includes(',') || value.includes('"') || value.includes('\n'))) {
          return `"${value.replace(/"/g, '""')}"`;
        }
        return value;
      }).join(',');
    });
    
    return [csvHeaders, ...csvRows].join('\n');
  },
  
  // Convert data to JSON format
  toJSON(data, pretty = false) {
    if (!data) {
      return '';
    }
    
    return JSON.stringify(data, null, pretty ? 2 : 0);
  },
  
  // Filter data based on selected fields
  filterFields(data, selectedFields) {
    if (!data || !selectedFields) {
      return data;
    }
    
    const fieldsToInclude = Object.keys(selectedFields).filter(key => selectedFields[key]);
    
    return data.map(item => {
      const filteredItem = {};
      fieldsToInclude.forEach(field => {
        filteredItem[field] = item[field] || '';
      });
      return filteredItem;
    });
  }
};

// File utilities
export const fileUtils = {
  // Download data as file
  downloadFile(content, filename, mimeType = 'text/plain') {
    const blob = new Blob([content], { type: mimeType });
    const url = URL.createObjectURL(blob);
    
    const link = document.createElement('a');
    link.href = url;
    link.download = filename;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    
    // Clean up the URL object
    setTimeout(() => URL.revokeObjectURL(url), 100);
  },
  
  // Copy text to clipboard
  async copyToClipboard(text) {
    try {
      if (navigator.clipboard && window.isSecureContext) {
        await navigator.clipboard.writeText(text);
      } else {
        // Fallback for older browsers
        const textArea = document.createElement('textarea');
        textArea.value = text;
        textArea.style.position = 'fixed';
        textArea.style.left = '-999999px';
        textArea.style.top = '-999999px';
        document.body.appendChild(textArea);
        textArea.focus();
        textArea.select();
        document.execCommand('copy');
        document.body.removeChild(textArea);
      }
      return true;
    } catch (error) {
      console.error('Failed to copy to clipboard:', error);
      return false;
    }
  }
};

// URL utilities
export const urlUtils = {
  // Check if URL is a JIRA page
  isJiraPage(url = window.location.href) {
    const hostname = new URL(url).hostname;
    return (
      hostname.includes('atlassian.net') ||
      hostname.includes('jira.') ||
      url.includes('/jira/') ||
      url.includes('/browse/') ||
      url.includes('/issues/')
    );
  },
  
  // Get JIRA issue key from URL
  getIssueKeyFromUrl(url = window.location.href) {
    const match = url.match(/[A-Z]+-\d+/);
    return match ? match[0] : null;
  },
  
  // Build GitLab project URL
  buildGitlabProjectUrl(gitlabApiUrl, projectPath) {
    if (!gitlabApiUrl || !projectPath) {
      return '';
    }
    
    const baseUrl = gitlabApiUrl.replace('/api/v4', '');
    return `${baseUrl}/${projectPath}`;
  }
};

// Date utilities
export const dateUtils = {
  // Format date to readable string
  formatDate(date, format = 'YYYY-MM-DD HH:mm:ss') {
    if (!date) return '';
    
    const d = new Date(date);
    if (isNaN(d.getTime())) return '';
    
    const year = d.getFullYear();
    const month = String(d.getMonth() + 1).padStart(2, '0');
    const day = String(d.getDate()).padStart(2, '0');
    const hours = String(d.getHours()).padStart(2, '0');
    const minutes = String(d.getMinutes()).padStart(2, '0');
    const seconds = String(d.getSeconds()).padStart(2, '0');
    
    return format
      .replace('YYYY', year)
      .replace('MM', month)
      .replace('DD', day)
      .replace('HH', hours)
      .replace('mm', minutes)
      .replace('ss', seconds);
  },
  
  // Get relative time string
  getRelativeTime(date) {
    if (!date) return '';
    
    const d = new Date(date);
    if (isNaN(d.getTime())) return '';
    
    const now = new Date();
    const diffMs = now - d;
    const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));
    const diffHours = Math.floor(diffMs / (1000 * 60 * 60));
    const diffMinutes = Math.floor(diffMs / (1000 * 60));
    
    if (diffDays > 0) {
      return `${diffDays} day${diffDays > 1 ? 's' : ''} ago`;
    } else if (diffHours > 0) {
      return `${diffHours} hour${diffHours > 1 ? 's' : ''} ago`;
    } else if (diffMinutes > 0) {
      return `${diffMinutes} minute${diffMinutes > 1 ? 's' : ''} ago`;
    } else {
      return 'Just now';
    }
  }
};

// Validation utilities
export const validation = {
  // Validate GitLab API URL
  isValidGitlabUrl(url) {
    if (!url) return false;
    
    try {
      const parsedUrl = new URL(url);
      return parsedUrl.protocol === 'http:' || parsedUrl.protocol === 'https:';
    } catch {
      return false;
    }
  },
  
  // Validate GitLab access token
  isValidGitlabToken(token) {
    return typeof token === 'string' && token.length > 0;
  },
  
  // Validate JIRA issue key
  isValidIssueKey(key) {
    return /^[A-Z]+-\d+$/.test(key);
  }
};

// Chrome extension utilities
export const chromeUtils = {
  // Send message to background script
  async sendMessage(message) {
    return new Promise((resolve, reject) => {
      chrome.runtime.sendMessage(message, (response) => {
        if (chrome.runtime.lastError) {
          reject(new Error(chrome.runtime.lastError.message));
        } else {
          resolve(response);
        }
      });
    });
  },
  
  // Get current tab
  async getCurrentTab() {
    return new Promise((resolve) => {
      chrome.tabs.query({ active: true, currentWindow: true }, (tabs) => {
        resolve(tabs[0] || null);
      });
    });
  },
  
  // Check if current page is JIRA
  async checkCurrentPage() {
    try {
      const tab = await this.getCurrentTab();
      if (!tab) return { isJiraPage: false };
      
      const response = await this.sendMessage({ action: 'checkPage' });
      return response.success ? response.pageInfo : { isJiraPage: false };
    } catch (error) {
      console.error('Error checking current page:', error);
      return { isJiraPage: false };
    }
  }
};

// Error handling utilities
export const errorUtils = {
  // Format error message for display
  formatError(error) {
    if (typeof error === 'string') {
      return error;
    }
    
    if (error && error.message) {
      return error.message;
    }
    
    return 'An unknown error occurred';
  },
  
  // Log error with context
  logError(error, context = '') {
    const message = this.formatError(error);
    console.error(`DutyRobot Error${context ? ` (${context})` : ''}:`, message, error);
  }
};

// Export GitLab service
export { gitlabService };

// Default export with all utilities
export default {
  storage,
  dataFormatter,
  fileUtils,
  urlUtils,
  dateUtils,
  validation,
  gitlabService,
  chromeUtils,
  errorUtils
};