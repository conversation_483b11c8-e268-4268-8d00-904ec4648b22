/**
 * taskManager.js
 * 任务管理系统，提供完整的任务CRUD功能和高级任务管理功能
 */

import { taskStorage, DEFAULT_DATA } from './storage';

/**
 * 任务状态枚举
 */
export const TASK_STATUS = {
  TODO: 'todo',
  IN_PROGRESS: 'in_progress',
  DONE: 'done',
  CANCELLED: 'cancelled'
};

/**
 * 任务优先级枚举
 */
export const TASK_PRIORITY = {
  LOW: 'low',
  MEDIUM: 'medium',
  HIGH: 'high',
  URGENT: 'urgent'
};

/**
 * 任务管理器
 */
class TaskManager {
  /**
   * 获取所有任务
   * @returns {Promise<Array>} 任务列表
   */
  async getAllTasks() {
    return await taskStorage.getAllTasks();
  }
  
  /**
   * 获取单个任务
   * @param {string} taskId - 任务ID
   * @returns {Promise<Object|null>} 任务对象或null
   */
  async getTask(taskId) {
    return await taskStorage.getTask(taskId);
  }
  
  /**
   * 创建新任务
   * @param {Object} taskData - 任务数据
   * @returns {Promise<Object|null>} 创建的任务或null
   */
  async createTask(taskData) {
    // 确保任务有默认状态和优先级
    const taskWithDefaults = {
      status: TASK_STATUS.TODO,
      priority: TASK_PRIORITY.MEDIUM,
      tags: [],
      ...taskData
    };
    
    // 添加时间戳
    const now = new Date().toISOString();
    taskWithDefaults.createdAt = now;
    taskWithDefaults.updatedAt = now;
    
    return await taskStorage.createTask(taskWithDefaults);
  }
  
  /**
   * 更新任务
   * @param {string} taskId - 任务ID
   * @param {Object} taskData - 更新的任务数据
   * @returns {Promise<Object|null>} 更新后的任务或null
   */
  async updateTask(taskId, taskData) {
    // 更新修改时间
    const updateData = {
      ...taskData,
      updatedAt: new Date().toISOString()
    };
    
    return await taskStorage.updateTask(taskId, updateData);
  }
  
  /**
   * 删除任务
   * @param {string} taskId - 任务ID
   * @returns {Promise<boolean>} 是否删除成功
   */
  async deleteTask(taskId) {
    return await taskStorage.deleteTask(taskId);
  }
  
  /**
   * 更改任务状态
   * @param {string} taskId - 任务ID
   * @param {string} newStatus - 新状态
   * @returns {Promise<Object|null>} 更新后的任务或null
   */
  async changeTaskStatus(taskId, newStatus) {
    // 验证状态是否有效
    if (!Object.values(TASK_STATUS).includes(newStatus)) {
      throw new Error(`Invalid task status: ${newStatus}`);
    }
    
    return await this.updateTask(taskId, { status: newStatus });
  }
  
  /**
   * 更改任务优先级
   * @param {string} taskId - 任务ID
   * @param {string} newPriority - 新优先级
   * @returns {Promise<Object|null>} 更新后的任务或null
   */
  async changeTaskPriority(taskId, newPriority) {
    // 验证优先级是否有效
    if (!Object.values(TASK_PRIORITY).includes(newPriority)) {
      throw new Error(`Invalid task priority: ${newPriority}`);
    }
    
    return await this.updateTask(taskId, { priority: newPriority });
  }
  
  /**
   * 添加任务标签
   * @param {string} taskId - 任务ID
   * @param {string} tag - 标签
   * @returns {Promise<Object|null>} 更新后的任务或null
   */
  async addTaskTag(taskId, tag) {
    const task = await this.getTask(taskId);
    if (!task) return null;
    
    // 如果标签已存在，则不重复添加
    if (task.tags && task.tags.includes(tag)) {
      return task;
    }
    
    // 添加新标签
    const updatedTags = [...(task.tags || []), tag];
    return await this.updateTask(taskId, { tags: updatedTags });
  }
  
  /**
   * 移除任务标签
   * @param {string} taskId - 任务ID
   * @param {string} tag - 标签
   * @returns {Promise<Object|null>} 更新后的任务或null
   */
  async removeTaskTag(taskId, tag) {
    const task = await this.getTask(taskId);
    if (!task || !task.tags) return task;
    
    // 移除标签
    const updatedTags = task.tags.filter(t => t !== tag);
    return await this.updateTask(taskId, { tags: updatedTags });
  }
  
  /**
   * 设置任务截止日期
   * @param {string} taskId - 任务ID
   * @param {string|Date} dueDate - 截止日期
   * @returns {Promise<Object|null>} 更新后的任务或null
   */
  async setTaskDueDate(taskId, dueDate) {
    // 转换日期格式
    let formattedDate = null;
    
    if (dueDate) {
      if (dueDate instanceof Date) {
        formattedDate = dueDate.toISOString();
      } else if (typeof dueDate === 'string') {
        // 尝试解析日期字符串
        try {
          formattedDate = new Date(dueDate).toISOString();
        } catch (e) {
          throw new Error('Invalid date format');
        }
      }
    }
    
    return await this.updateTask(taskId, { dueDate: formattedDate });
  }
  
  /**
   * 获取即将到期的任务
   * @param {number} daysThreshold - 天数阈值
   * @returns {Promise<Array>} 即将到期的任务列表
   */
  async getUpcomingTasks(daysThreshold = 3) {
    const tasks = await this.getAllTasks();
    const now = new Date();
    const thresholdDate = new Date(now);
    thresholdDate.setDate(now.getDate() + daysThreshold);
    
    return tasks.filter(task => {
      // 只考虑有截止日期且未完成的任务
      if (!task.dueDate || task.status === TASK_STATUS.DONE || task.status === TASK_STATUS.CANCELLED) {
        return false;
      }
      
      const dueDate = new Date(task.dueDate);
      return dueDate <= thresholdDate && dueDate >= now;
    });
  }
  
  /**
   * 获取已逾期的任务
   * @returns {Promise<Array>} 已逾期的任务列表
   */
  async getOverdueTasks() {
    const tasks = await this.getAllTasks();
    const now = new Date();
    
    return tasks.filter(task => {
      // 只考虑有截止日期且未完成的任务
      if (!task.dueDate || task.status === TASK_STATUS.DONE || task.status === TASK_STATUS.CANCELLED) {
        return false;
      }
      
      const dueDate = new Date(task.dueDate);
      return dueDate < now;
    });
  }
  
  /**
   * 按状态筛选任务
   * @param {string} status - 任务状态
   * @returns {Promise<Array>} 筛选后的任务列表
   */
  async getTasksByStatus(status) {
    return await taskStorage.getTasksByStatus(status);
  }
  
  /**
   * 按标签筛选任务
   * @param {string} tag - 标签
   * @returns {Promise<Array>} 筛选后的任务列表
   */
  async getTasksByTag(tag) {
    return await taskStorage.getTasksByTag(tag);
  }
  
  /**
   * 按优先级筛选任务
   * @param {string} priority - 优先级
   * @returns {Promise<Array>} 筛选后的任务列表
   */
  async getTasksByPriority(priority) {
    const tasks = await this.getAllTasks();
    return tasks.filter(task => task.priority === priority);
  }
  
  /**
   * 搜索任务
   * @param {string} query - 搜索关键词
   * @returns {Promise<Array>} 搜索结果
   */
  async searchTasks(query) {
    return await taskStorage.searchTasks(query);
  }
  
  /**
   * 获取任务统计信息
   * @returns {Promise<Object>} 统计信息
   */
  async getTasksStats() {
    const tasks = await this.getAllTasks();
    
    // 初始化统计对象
    const stats = {
      total: tasks.length,
      byStatus: {
        todo: 0,
        in_progress: 0,
        done: 0,
        cancelled: 0
      },
      byPriority: {
        low: 0,
        medium: 0,
        high: 0,
        urgent: 0
      },
      overdue: 0,
      upcoming: 0,
      withoutDueDate: 0
    };
    
    // 计算统计信息
    const now = new Date();
    const thresholdDate = new Date(now);
    thresholdDate.setDate(now.getDate() + 3); // 3天内到期为即将到期
    
    tasks.forEach(task => {
      // 按状态统计
      if (stats.byStatus[task.status] !== undefined) {
        stats.byStatus[task.status]++;
      }
      
      // 按优先级统计
      if (stats.byPriority[task.priority] !== undefined) {
        stats.byPriority[task.priority]++;
      }
      
      // 截止日期统计
      if (task.dueDate) {
        const dueDate = new Date(task.dueDate);
        if (dueDate < now && task.status !== TASK_STATUS.DONE && task.status !== TASK_STATUS.CANCELLED) {
          stats.overdue++;
        } else if (dueDate <= thresholdDate && dueDate >= now && task.status !== TASK_STATUS.DONE && task.status !== TASK_STATUS.CANCELLED) {
          stats.upcoming++;
        }
      } else {
        stats.withoutDueDate++;
      }
    });
    
    return stats;
  }
  
  /**
   * 批量更新任务状态
   * @param {Array<string>} taskIds - 任务ID数组
   * @param {string} newStatus - 新状态
   * @returns {Promise<Array>} 更新后的任务列表
   */
  async batchUpdateStatus(taskIds, newStatus) {
    // 验证状态是否有效
    if (!Object.values(TASK_STATUS).includes(newStatus)) {
      throw new Error(`Invalid task status: ${newStatus}`);
    }
    
    const updatePromises = taskIds.map(id => this.changeTaskStatus(id, newStatus));
    const results = await Promise.all(updatePromises);
    return results.filter(Boolean); // 过滤掉null结果
  }
  
  /**
   * 批量更新任务优先级
   * @param {Array<string>} taskIds - 任务ID数组
   * @param {string} newPriority - 新优先级
   * @returns {Promise<Array>} 更新后的任务列表
   */
  async batchUpdatePriority(taskIds, newPriority) {
    // 验证优先级是否有效
    if (!Object.values(TASK_PRIORITY).includes(newPriority)) {
      throw new Error(`Invalid task priority: ${newPriority}`);
    }
    
    const updatePromises = taskIds.map(id => this.changeTaskPriority(id, newPriority));
    const results = await Promise.all(updatePromises);
    return results.filter(Boolean); // 过滤掉null结果
  }
  
  /**
   * 批量添加标签
   * @param {Array<string>} taskIds - 任务ID数组
   * @param {string} tag - 标签
   * @returns {Promise<Array>} 更新后的任务列表
   */
  async batchAddTag(taskIds, tag) {
    const updatePromises = taskIds.map(id => this.addTaskTag(id, tag));
    const results = await Promise.all(updatePromises);
    return results.filter(Boolean); // 过滤掉null结果
  }
  
  /**
   * 批量删除任务
   * @param {Array<string>} taskIds - 任务ID数组
   * @returns {Promise<number>} 成功删除的任务数量
   */
  async batchDeleteTasks(taskIds) {
    const deletePromises = taskIds.map(id => this.deleteTask(id));
    const results = await Promise.all(deletePromises);
    return results.filter(Boolean).length; // 计算成功删除的数量
  }
}

// 导出任务管理器实例
const taskManager = new TaskManager();
export default taskManager;