/**
 * gitlab.js
 * GitLab API基础集成，提供API认证和基础请求封装
 */

import { storage } from './index';

// GitLab API版本
const API_VERSION = 'v4';

// 请求超时时间（毫秒）
const REQUEST_TIMEOUT = 30000;

// 重试次数
const MAX_RETRIES = 3;

// 重试延迟（毫秒）
const RETRY_DELAY = 1000;

/**
 * GitLab API服务
 */
class GitLabService {
  constructor() {
    this.token = null;
    this.baseUrl = null;
    this.projectId = null;
    this.initialized = false;
  }

  /**
   * 初始化GitLab服务
   * @returns {Promise<boolean>} 初始化是否成功
   */
  async initialize() {
    try {
      const settings = await storage.getSettings();
      
      if (!settings?.gitlabAccessToken || !settings?.gitlabApiUrl) {
        console.warn('GitLab settings not configured');
        return false;
      }
      
      this.token = settings.gitlabAccessToken;
      this.baseUrl = settings.gitlabApiUrl.endsWith('/') 
        ? settings.gitlabApiUrl.slice(0, -1) 
        : settings.gitlabApiUrl;
      this.projectId = settings.projectId || '';
      this.initialized = true;
      
      return true;
    } catch (error) {
      console.error('Failed to initialize GitLab service:', error);
      return false;
    }
  }

  /**
   * 检查服务是否已初始化
   * @returns {boolean} 是否已初始化
   * @private
   */
  _checkInitialized() {
    if (!this.initialized) {
      throw new Error('GitLab service not initialized. Call initialize() first.');
    }
    return true;
  }

  /**
   * 构建API URL
   * @param {string} endpoint - API端点
   * @returns {string} 完整的API URL
   * @private
   */
  _buildApiUrl(endpoint) {
    return `${this.baseUrl}/api/${API_VERSION}/${endpoint}`;
  }

  /**
   * 发送API请求
   * @param {string} endpoint - API端点
   * @param {Object} options - 请求选项
   * @param {number} retries - 当前重试次数
   * @returns {Promise<Object>} 请求结果
   * @private
   */
  async _request(endpoint, options = {}, retries = 0) {
    this._checkInitialized();
    
    const url = this._buildApiUrl(endpoint);
    
    const fetchOptions = {
      method: options.method || 'GET',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${this.token}`,
        ...options.headers
      },
      timeout: REQUEST_TIMEOUT
    };
    
    if (options.body) {
      fetchOptions.body = JSON.stringify(options.body);
    }
    
    try {
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), REQUEST_TIMEOUT);
      
      fetchOptions.signal = controller.signal;
      
      const response = await fetch(url, fetchOptions);
      clearTimeout(timeoutId);
      
      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`GitLab API error (${response.status}): ${errorText}`);
      }
      
      return await response.json();
    } catch (error) {
      // 处理超时或网络错误
      if (error.name === 'AbortError') {
        throw new Error('GitLab API request timed out');
      }
      
      // 重试逻辑
      if (retries < MAX_RETRIES) {
        console.warn(`GitLab API request failed, retrying (${retries + 1}/${MAX_RETRIES})...`);
        await new Promise(resolve => setTimeout(resolve, RETRY_DELAY));
        return this._request(endpoint, options, retries + 1);
      }
      
      throw error;
    }
  }

  /**
   * 获取项目信息
   * @param {string} projectId - 项目ID（可选，默认使用配置的项目ID）
   * @returns {Promise<Object>} 项目信息
   */
  async getProject(projectId = null) {
    const id = projectId || this.projectId;
    
    if (!id) {
      throw new Error('Project ID is required');
    }
    
    return this._request(`projects/${encodeURIComponent(id)}`);
  }

  /**
   * 获取项目合并请求列表
   * @param {string} projectId - 项目ID（可选，默认使用配置的项目ID）
   * @param {Object} options - 查询选项
   * @param {string} options.state - MR状态 ('opened', 'closed', 'locked', 'merged')
   * @param {number} options.page - 页码
   * @param {number} options.perPage - 每页数量
   * @returns {Promise<Array>} 合并请求列表
   */
  async getMergeRequests(projectId = null, options = {}) {
    const id = projectId || this.projectId;
    
    if (!id) {
      throw new Error('Project ID is required');
    }
    
    const queryParams = new URLSearchParams();
    
    if (options.state) queryParams.append('state', options.state);
    if (options.page) queryParams.append('page', options.page);
    if (options.perPage) queryParams.append('per_page', options.perPage);
    
    const endpoint = `projects/${encodeURIComponent(id)}/merge_requests${queryParams.toString() ? '?' + queryParams.toString() : ''}`;
    
    return this._request(endpoint);
  }

  /**
   * 获取特定合并请求详情
   * @param {string} projectId - 项目ID（可选，默认使用配置的项目ID）
   * @param {number} mergeRequestIid - 合并请求IID
   * @returns {Promise<Object>} 合并请求详情
   */
  async getMergeRequest(projectId = null, mergeRequestIid) {
    const id = projectId || this.projectId;
    
    if (!id) {
      throw new Error('Project ID is required');
    }
    
    if (!mergeRequestIid) {
      throw new Error('Merge Request IID is required');
    }
    
    return this._request(`projects/${encodeURIComponent(id)}/merge_requests/${mergeRequestIid}`);
  }

  /**
   * 获取项目分支列表
   * @param {string} projectId - 项目ID（可选，默认使用配置的项目ID）
   * @param {Object} options - 查询选项
   * @param {string} options.search - 搜索关键词
   * @param {number} options.page - 页码
   * @param {number} options.perPage - 每页数量
   * @returns {Promise<Array>} 分支列表
   */
  async getBranches(projectId = null, options = {}) {
    const id = projectId || this.projectId;
    
    if (!id) {
      throw new Error('Project ID is required');
    }
    
    const queryParams = new URLSearchParams();
    
    if (options.search) queryParams.append('search', options.search);
    if (options.page) queryParams.append('page', options.page);
    if (options.perPage) queryParams.append('per_page', options.perPage);
    
    const endpoint = `projects/${encodeURIComponent(id)}/repository/branches${queryParams.toString() ? '?' + queryParams.toString() : ''}`;
    
    return this._request(endpoint);
  }
}

// 导出GitLab服务实例
export const gitlabService = new GitLabService();