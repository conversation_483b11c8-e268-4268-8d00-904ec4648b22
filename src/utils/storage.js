/**
 * storage.js
 * Chrome Storage API封装，提供统一的数据存储和管理系统
 */

// 存储键名常量
const STORAGE_KEYS = {
  TASKS: 'dutyRobot_tasks',
  SETTINGS: 'dutyRobot_settings',
  NOTIFICATIONS: 'dutyRobot_notifications'
};

// 默认数据结构
const DEFAULT_DATA = {
  // 默认设置
  settings: {
    region: 'cn',
    jira: {
      url: '',
      username: '',
      apiToken: ''
    },
    gitlab: {
      url: '',
      token: '',
      projectId: ''
    },
    notifications: {
      enabled: true,
      reminderInterval: 30, // 分钟
      soundEnabled: true
    },
    extractFields: {
      issueKey: true,
      summary: true,
      status: true,
      assignee: true,
      priority: true,
      issueType: true,
      created: true,
      updated: true,
      description: false,
      comments: false
    },
    maxExtractCount: 100,
    defaultExportFormat: 'csv'
  },
  
  // 默认任务结构
  taskTemplate: {
    id: '',
    title: '',
    description: '',
    status: 'todo', // 'todo', 'in_progress', 'done', 'cancelled'
    priority: 'medium', // 'low', 'medium', 'high'
    tags: [],
    jiraKey: '',
    gitlabMR: '',
    assignee: '',
    createdAt: null,
    updatedAt: null,
    dueDate: null
  },
  
  // 默认通知结构
  notificationTemplate: {
    id: '',
    type: 'info', // 'info', 'warning', 'error', 'success'
    title: '',
    message: '',
    read: false,
    createdAt: null,
    relatedTaskId: null
  }
};

/**
 * 数据验证函数
 */
const validators = {
  // 验证任务数据
  validateTask(task) {
    if (!task) return false;
    if (!task.id) return false;
    if (!task.title) return false;
    if (!['todo', 'in_progress', 'done', 'cancelled'].includes(task.status)) {
      return false;
    }
    return true;
  },
  
  // 验证设置数据
  validateSettings(settings) {
    if (!settings) return false;
    
    // 验证JIRA URL (如果提供)
    if (settings.jira && settings.jira.url) {
      try {
        new URL(settings.jira.url);
      } catch (e) {
        return false;
      }
    }
    
    // 验证GitLab URL (如果提供)
    if (settings.gitlab && settings.gitlab.url) {
      try {
        new URL(settings.gitlab.url);
      } catch (e) {
        return false;
      }
    }
    
    return true;
  },
  
  // 验证通知数据
  validateNotification(notification) {
    if (!notification) return false;
    if (!notification.id) return false;
    if (!notification.title) return false;
    if (!['info', 'warning', 'error', 'success'].includes(notification.type)) {
      return false;
    }
    return true;
  }
};

/**
 * 存储服务
 */
const storageService = {
  /**
   * 获取存储数据
   * @param {string} key - 存储键名
   * @param {any} defaultValue - 默认值
   * @returns {Promise<any>} 存储的数据
   */
  async get(key, defaultValue = null) {
    return new Promise((resolve) => {
      chrome.storage.sync.get([key], (result) => {
        resolve(result[key] !== undefined ? result[key] : defaultValue);
      });
    });
  },
  
  /**
   * 设置存储数据
   * @param {string} key - 存储键名
   * @param {any} value - 要存储的数据
   * @returns {Promise<void>}
   */
  async set(key, value) {
    return new Promise((resolve, reject) => {
      chrome.storage.sync.set({ [key]: value }, () => {
        if (chrome.runtime.lastError) {
          reject(new Error(chrome.runtime.lastError.message));
        } else {
          resolve();
        }
      });
    });
  },
  
  /**
   * 删除存储数据
   * @param {string} key - 存储键名
   * @returns {Promise<void>}
   */
  async remove(key) {
    return new Promise((resolve, reject) => {
      chrome.storage.sync.remove(key, () => {
        if (chrome.runtime.lastError) {
          reject(new Error(chrome.runtime.lastError.message));
        } else {
          resolve();
        }
      });
    });
  },
  
  /**
   * 清除所有存储数据
   * @returns {Promise<void>}
   */
  async clear() {
    return new Promise((resolve, reject) => {
      chrome.storage.sync.clear(() => {
        if (chrome.runtime.lastError) {
          reject(new Error(chrome.runtime.lastError.message));
        } else {
          resolve();
        }
      });
    });
  }
};

/**
 * 任务存储服务
 */
export const taskStorage = {
  /**
   * 获取所有任务
   * @returns {Promise<Array>} 任务列表
   */
  async getAllTasks() {
    try {
      const tasks = await storageService.get(STORAGE_KEYS.TASKS, []);
      return Array.isArray(tasks) ? tasks : [];
    } catch (error) {
      console.error('Error getting tasks:', error);
      return [];
    }
  },
  
  /**
   * 获取单个任务
   * @param {string} taskId - 任务ID
   * @returns {Promise<Object|null>} 任务对象或null
   */
  async getTask(taskId) {
    try {
      const tasks = await this.getAllTasks();
      return tasks.find(task => task.id === taskId) || null;
    } catch (error) {
      console.error(`Error getting task ${taskId}:`, error);
      return null;
    }
  },
  
  /**
   * 创建新任务
   * @param {Object} taskData - 任务数据
   * @returns {Promise<Object|null>} 创建的任务或null
   */
  async createTask(taskData) {
    try {
      const tasks = await this.getAllTasks();
      
      // 创建新任务，合并默认模板
      const newTask = {
        ...DEFAULT_DATA.taskTemplate,
        ...taskData,
        id: taskData.id || `task_${Date.now()}`,
        createdAt: taskData.createdAt || new Date().toISOString(),
        updatedAt: new Date().toISOString()
      };
      
      // 验证任务数据
      if (!validators.validateTask(newTask)) {
        throw new Error('Invalid task data');
      }
      
      // 添加到任务列表并保存
      tasks.push(newTask);
      await storageService.set(STORAGE_KEYS.TASKS, tasks);
      
      return newTask;
    } catch (error) {
      console.error('Error creating task:', error);
      return null;
    }
  },
  
  /**
   * 更新任务
   * @param {string} taskId - 任务ID
   * @param {Object} taskData - 更新的任务数据
   * @returns {Promise<Object|null>} 更新后的任务或null
   */
  async updateTask(taskId, taskData) {
    try {
      const tasks = await this.getAllTasks();
      const taskIndex = tasks.findIndex(task => task.id === taskId);
      
      if (taskIndex === -1) {
        throw new Error(`Task with ID ${taskId} not found`);
      }
      
      // 更新任务，保留原有ID和创建时间
      const updatedTask = {
        ...tasks[taskIndex],
        ...taskData,
        id: taskId, // 确保ID不变
        createdAt: tasks[taskIndex].createdAt, // 保留创建时间
        updatedAt: new Date().toISOString() // 更新修改时间
      };
      
      // 验证任务数据
      if (!validators.validateTask(updatedTask)) {
        throw new Error('Invalid task data');
      }
      
      // 更新任务列表并保存
      tasks[taskIndex] = updatedTask;
      await storageService.set(STORAGE_KEYS.TASKS, tasks);
      
      return updatedTask;
    } catch (error) {
      console.error(`Error updating task ${taskId}:`, error);
      return null;
    }
  },
  
  /**
   * 删除任务
   * @param {string} taskId - 任务ID
   * @returns {Promise<boolean>} 是否删除成功
   */
  async deleteTask(taskId) {
    try {
      const tasks = await this.getAllTasks();
      const filteredTasks = tasks.filter(task => task.id !== taskId);
      
      if (filteredTasks.length === tasks.length) {
        // 没有找到要删除的任务
        return false;
      }
      
      await storageService.set(STORAGE_KEYS.TASKS, filteredTasks);
      return true;
    } catch (error) {
      console.error(`Error deleting task ${taskId}:`, error);
      return false;
    }
  },
  
  /**
   * 按状态筛选任务
   * @param {string} status - 任务状态
   * @returns {Promise<Array>} 筛选后的任务列表
   */
  async getTasksByStatus(status) {
    try {
      const tasks = await this.getAllTasks();
      return tasks.filter(task => task.status === status);
    } catch (error) {
      console.error(`Error getting tasks by status ${status}:`, error);
      return [];
    }
  },
  
  /**
   * 按标签筛选任务
   * @param {string} tag - 标签
   * @returns {Promise<Array>} 筛选后的任务列表
   */
  async getTasksByTag(tag) {
    try {
      const tasks = await this.getAllTasks();
      return tasks.filter(task => task.tags && task.tags.includes(tag));
    } catch (error) {
      console.error(`Error getting tasks by tag ${tag}:`, error);
      return [];
    }
  },
  
  /**
   * 搜索任务
   * @param {string} query - 搜索关键词
   * @returns {Promise<Array>} 搜索结果
   */
  async searchTasks(query) {
    try {
      if (!query) return [];
      
      const tasks = await this.getAllTasks();
      const lowerQuery = query.toLowerCase();
      
      return tasks.filter(task => {
        return (
          task.title.toLowerCase().includes(lowerQuery) ||
          (task.description && task.description.toLowerCase().includes(lowerQuery)) ||
          (task.jiraKey && task.jiraKey.toLowerCase().includes(lowerQuery))
        );
      });
    } catch (error) {
      console.error(`Error searching tasks with query "${query}":`, error);
      return [];
    }
  }
};

/**
 * 设置存储服务
 */
export const settingsStorage = {
  /**
   * 获取所有设置
   * @returns {Promise<Object>} 设置对象
   */
  async getSettings() {
    try {
      const settings = await storageService.get(STORAGE_KEYS.SETTINGS, DEFAULT_DATA.settings);
      return settings;
    } catch (error) {
      console.error('Error getting settings:', error);
      return DEFAULT_DATA.settings;
    }
  },
  
  /**
   * 更新设置
   * @param {Object} newSettings - 新设置
   * @returns {Promise<Object|null>} 更新后的设置或null
   */
  async updateSettings(newSettings) {
    try {
      const currentSettings = await this.getSettings();
      
      // 合并设置
      const updatedSettings = {
        ...currentSettings,
        ...newSettings
      };
      
      // 验证设置
      if (!validators.validateSettings(updatedSettings)) {
        throw new Error('Invalid settings data');
      }
      
      await storageService.set(STORAGE_KEYS.SETTINGS, updatedSettings);
      return updatedSettings;
    } catch (error) {
      console.error('Error updating settings:', error);
      return null;
    }
  },
  
  /**
   * 重置设置为默认值
   * @returns {Promise<Object>} 默认设置
   */
  async resetSettings() {
    try {
      await storageService.set(STORAGE_KEYS.SETTINGS, DEFAULT_DATA.settings);
      return DEFAULT_DATA.settings;
    } catch (error) {
      console.error('Error resetting settings:', error);
      return null;
    }
  }
};

/**
 * 通知存储服务
 */
export const notificationStorage = {
  /**
   * 获取所有通知
   * @returns {Promise<Array>} 通知列表
   */
  async getAllNotifications() {
    try {
      const notifications = await storageService.get(STORAGE_KEYS.NOTIFICATIONS, []);
      return Array.isArray(notifications) ? notifications : [];
    } catch (error) {
      console.error('Error getting notifications:', error);
      return [];
    }
  },
  
  /**
   * 获取未读通知
   * @returns {Promise<Array>} 未读通知列表
   */
  async getUnreadNotifications() {
    try {
      const notifications = await this.getAllNotifications();
      return notifications.filter(notification => !notification.read);
    } catch (error) {
      console.error('Error getting unread notifications:', error);
      return [];
    }
  },
  
  /**
   * 创建新通知
   * @param {Object} notificationData - 通知数据
   * @returns {Promise<Object|null>} 创建的通知或null
   */
  async createNotification(notificationData) {
    try {
      const notifications = await this.getAllNotifications();
      
      // 创建新通知，合并默认模板
      const newNotification = {
        ...DEFAULT_DATA.notificationTemplate,
        ...notificationData,
        id: notificationData.id || `notification_${Date.now()}`,
        createdAt: notificationData.createdAt || new Date().toISOString(),
        read: false
      };
      
      // 验证通知数据
      if (!validators.validateNotification(newNotification)) {
        throw new Error('Invalid notification data');
      }
      
      // 添加到通知列表并保存
      notifications.push(newNotification);
      await storageService.set(STORAGE_KEYS.NOTIFICATIONS, notifications);
      
      return newNotification;
    } catch (error) {
      console.error('Error creating notification:', error);
      return null;
    }
  },
  
  /**
   * 标记通知为已读
   * @param {string} notificationId - 通知ID
   * @returns {Promise<boolean>} 是否标记成功
   */
  async markAsRead(notificationId) {
    try {
      const notifications = await this.getAllNotifications();
      const notificationIndex = notifications.findIndex(notification => notification.id === notificationId);
      
      if (notificationIndex === -1) {
        return false;
      }
      
      notifications[notificationIndex].read = true;
      await storageService.set(STORAGE_KEYS.NOTIFICATIONS, notifications);
      
      return true;
    } catch (error) {
      console.error(`Error marking notification ${notificationId} as read:`, error);
      return false;
    }
  },
  
  /**
   * 标记所有通知为已读
   * @returns {Promise<boolean>} 是否标记成功
   */
  async markAllAsRead() {
    try {
      const notifications = await this.getAllNotifications();
      
      const updatedNotifications = notifications.map(notification => ({
        ...notification,
        read: true
      }));
      
      await storageService.set(STORAGE_KEYS.NOTIFICATIONS, updatedNotifications);
      return true;
    } catch (error) {
      console.error('Error marking all notifications as read:', error);
      return false;
    }
  },
  
  /**
   * 删除通知
   * @param {string} notificationId - 通知ID
   * @returns {Promise<boolean>} 是否删除成功
   */
  async deleteNotification(notificationId) {
    try {
      const notifications = await this.getAllNotifications();
      const filteredNotifications = notifications.filter(notification => notification.id !== notificationId);
      
      if (filteredNotifications.length === notifications.length) {
        // 没有找到要删除的通知
        return false;
      }
      
      await storageService.set(STORAGE_KEYS.NOTIFICATIONS, filteredNotifications);
      return true;
    } catch (error) {
      console.error(`Error deleting notification ${notificationId}:`, error);
      return false;
    }
  },
  
  /**
   * 清除所有通知
   * @returns {Promise<boolean>} 是否清除成功
   */
  async clearAllNotifications() {
    try {
      await storageService.set(STORAGE_KEYS.NOTIFICATIONS, []);
      return true;
    } catch (error) {
      console.error('Error clearing all notifications:', error);
      return false;
    }
  }
};

// 导出存储服务
export default {
  taskStorage,
  settingsStorage,
  notificationStorage,
  DEFAULT_DATA,
  STORAGE_KEYS
};