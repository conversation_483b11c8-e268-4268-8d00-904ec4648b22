/**
 * notifications.js
 * Chrome Notifications API封装，提供统一的通知功能
 */

import { notificationStorage } from './storage';

// 通知类型常量
const NOTIFICATION_TYPES = {
  TEXT: 'basic',      // 基础文本通知
  PROGRESS: 'progress', // 进度通知
  LIST: 'list',       // 列表通知
  IMAGE: 'image'      // 图片通知
};

// 通知优先级常量
const NOTIFICATION_PRIORITY = {
  LOW: 0,
  DEFAULT: 1,
  HIGH: 2
};

// 默认图标路径
const DEFAULT_ICON = {
  '16': 'icons/icon16.svg',
  '48': 'icons/icon48.svg',
  '128': 'icons/icon128.svg'
};

/**
 * 通知服务
 */
class NotificationService {
  constructor() {
    // 初始化通知点击监听器
    this._setupEventListeners();
    
    // 存储通知回调函数的映射
    this._clickHandlers = new Map();
  }
  
  /**
   * 设置事件监听器
   * @private
   */
  _setupEventListeners() {
    // 监听通知点击事件
    chrome.notifications.onClicked.addListener(this._handleNotificationClick.bind(this));
    
    // 监听通知关闭事件
    chrome.notifications.onClosed.addListener(this._handleNotificationClosed.bind(this));
    
    // 监听通知按钮点击事件
    chrome.notifications.onButtonClicked.addListener(this._handleButtonClick.bind(this));
  }
  
  /**
   * 处理通知点击事件
   * @param {string} notificationId - 通知ID
   * @private
   */
  _handleNotificationClick(notificationId) {
    // 标记通知为已读
    notificationStorage.markAsRead(notificationId);
    
    // 执行注册的点击回调
    if (this._clickHandlers.has(notificationId)) {
      this._clickHandlers.get(notificationId)();
      this._clickHandlers.delete(notificationId);
    }
  }
  
  /**
   * 处理通知关闭事件
   * @param {string} notificationId - 通知ID
   * @param {boolean} byUser - 是否由用户关闭
   * @private
   */
  _handleNotificationClosed(notificationId, byUser) {
    // 清理点击处理器
    if (this._clickHandlers.has(notificationId)) {
      this._clickHandlers.delete(notificationId);
    }
  }
  
  /**
   * 处理通知按钮点击事件
   * @param {string} notificationId - 通知ID
   * @param {number} buttonIndex - 按钮索引
   * @private
   */
  _handleButtonClick(notificationId, buttonIndex) {
    // 可以在这里添加按钮点击的处理逻辑
    console.log(`Button ${buttonIndex} clicked on notification ${notificationId}`);
  }
  
  /**
   * 检查通知权限
   * @returns {Promise<boolean>} 是否有通知权限
   */
  async checkPermission() {
    return new Promise((resolve) => {
      // Chrome扩展已经请求了notifications权限，所以这里主要检查用户设置
      chrome.storage.sync.get(['showNotifications'], (result) => {
        resolve(result.showNotifications !== false);
      });
    });
  }
  
  /**
   * 创建基础文本通知
   * @param {string} title - 通知标题
   * @param {string} message - 通知内容
   * @param {Object} options - 通知选项
   * @param {Function} onClick - 点击通知的回调函数
   * @returns {Promise<string>} 通知ID
   */
  async createTextNotification(title, message, options = {}, onClick = null) {
    const hasPermission = await this.checkPermission();
    if (!hasPermission) return null;
    
    const notificationId = options.id || `notification_${Date.now()}`;
    
    const notificationOptions = {
      type: NOTIFICATION_TYPES.TEXT,
      title: title,
      message: message,
      iconUrl: options.iconUrl || DEFAULT_ICON['48'],
      priority: options.priority || NOTIFICATION_PRIORITY.DEFAULT,
      requireInteraction: options.requireInteraction || false
    };
    
    // 如果提供了按钮，添加到选项中
    if (options.buttons && Array.isArray(options.buttons)) {
      notificationOptions.buttons = options.buttons.slice(0, 2); // Chrome最多支持2个按钮
    }
    
    // 创建Chrome通知
    return new Promise((resolve) => {
      chrome.notifications.create(notificationId, notificationOptions, (id) => {
        // 存储点击回调
        if (onClick) {
          this._clickHandlers.set(id, onClick);
        }
        
        // 同时在存储中创建通知记录
        this._saveNotificationToStorage(id, title, message, options.type || 'info', options.relatedTaskId);
        
        resolve(id);
      });
    });
  }
  
  /**
   * 创建进度通知
   * @param {string} title - 通知标题
   * @param {string} message - 通知内容
   * @param {number} progress - 进度值(0-100)
   * @param {Object} options - 通知选项
   * @returns {Promise<string>} 通知ID
   */
  async createProgressNotification(title, message, progress, options = {}) {
    const hasPermission = await this.checkPermission();
    if (!hasPermission) return null;
    
    const notificationId = options.id || `progress_notification_${Date.now()}`;
    
    const notificationOptions = {
      type: NOTIFICATION_TYPES.PROGRESS,
      title: title,
      message: message,
      iconUrl: options.iconUrl || DEFAULT_ICON['48'],
      progress: Math.max(0, Math.min(100, progress)), // 确保进度在0-100之间
      requireInteraction: options.requireInteraction || false
    };
    
    // 创建Chrome通知
    return new Promise((resolve) => {
      chrome.notifications.create(notificationId, notificationOptions, (id) => {
        // 同时在存储中创建通知记录
        this._saveNotificationToStorage(id, title, message, 'info', options.relatedTaskId);
        
        resolve(id);
      });
    });
  }
  
  /**
   * 更新进度通知
   * @param {string} notificationId - 通知ID
   * @param {Object} options - 更新选项
   * @returns {Promise<boolean>} 是否更新成功
   */
  async updateProgressNotification(notificationId, options = {}) {
    return new Promise((resolve) => {
      const updateOptions = {};
      
      if (options.title) updateOptions.title = options.title;
      if (options.message) updateOptions.message = options.message;
      if (options.progress !== undefined) {
        updateOptions.progress = Math.max(0, Math.min(100, options.progress));
      }
      
      chrome.notifications.update(notificationId, updateOptions, (wasUpdated) => {
        resolve(wasUpdated);
      });
    });
  }
  
  /**
   * 清除通知
   * @param {string} notificationId - 通知ID
   * @returns {Promise<boolean>} 是否清除成功
   */
  async clearNotification(notificationId) {
    return new Promise((resolve) => {
      chrome.notifications.clear(notificationId, (wasCleared) => {
        if (wasCleared) {
          // 清理点击处理器
          if (this._clickHandlers.has(notificationId)) {
            this._clickHandlers.delete(notificationId);
          }
        }
        resolve(wasCleared);
      });
    });
  }
  
  /**
   * 清除所有通知
   * @returns {Promise<boolean>} 是否清除成功
   */
  async clearAllNotifications() {
    return new Promise((resolve) => {
      chrome.notifications.getAll((notifications) => {
        const notificationIds = Object.keys(notifications);
        let clearedCount = 0;
        
        if (notificationIds.length === 0) {
          resolve(true);
          return;
        }
        
        notificationIds.forEach((id) => {
          chrome.notifications.clear(id, (wasCleared) => {
            clearedCount++;
            
            if (wasCleared && this._clickHandlers.has(id)) {
              this._clickHandlers.delete(id);
            }
            
            if (clearedCount === notificationIds.length) {
              resolve(true);
            }
          });
        });
      });
    });
  }
  
  /**
   * 将通知保存到存储中
   * @param {string} id - 通知ID
   * @param {string} title - 通知标题
   * @param {string} message - 通知内容
   * @param {string} type - 通知类型
   * @param {string} relatedTaskId - 相关任务ID
   * @private
   */
  async _saveNotificationToStorage(id, title, message, type = 'info', relatedTaskId = null) {
    try {
      await notificationStorage.createNotification({
        id,
        title,
        message,
        type,
        relatedTaskId,
        createdAt: new Date().toISOString(),
        read: false
      });
    } catch (error) {
      console.error('Error saving notification to storage:', error);
    }
  }
}

// 导出通知服务实例
const notificationService = new NotificationService();

export {
  notificationService,
  NOTIFICATION_TYPES,
  NOTIFICATION_PRIORITY
};

export default notificationService;