# SuperDutyRobot 项目结构设计

## 整体架构概览

```
superDutyRobot/
├── 📁 src/                          # 源代码目录
│   ├── 📁 background/                # Background Service Worker
│   ├── 📁 content-scripts/           # 内容脚本
│   ├── 📁 popup/                     # 弹窗界面
│   ├── 📁 options/                   # 设置页面
│   ├── 📁 components/                # 共享组件
│   ├── 📁 services/                  # 业务服务层
│   ├── 📁 utils/                     # 工具函数
│   ├── 📁 store/                     # 状态管理
│   ├── 📁 types/                     # TypeScript类型定义
│   └── 📁 assets/                    # 静态资源
├── 📁 public/                        # 公共资源
├── 📁 docs/                          # 文档
├── 📁 tests/                         # 测试文件
└── 📄 配置文件
```

## 详细目录结构

### 1. 源代码目录 (`src/`)

#### 1.1 Background Service Worker (`src/background/`)
```
src/background/
├── background.js                     # 主入口文件
├── 📁 handlers/                      # 事件处理器
│   ├── messageHandler.js             # 消息处理
│   ├── alarmHandler.js               # 定时任务处理
│   ├── notificationHandler.js        # 通知处理
│   └── storageHandler.js             # 存储处理
├── 📁 services/                      # 后台服务
│   ├── taskService.js                # 任务服务
│   ├── reminderService.js            # 提醒服务
│   ├── syncService.js                # 数据同步服务
│   └── integrationService.js         # 外部集成服务
└── 📁 workers/                       # 工作线程
    ├── dataProcessor.js              # 数据处理器
    └── statusMonitor.js              # 状态监控器
```

#### 1.2 内容脚本 (`src/content-scripts/`)
```
src/content-scripts/
├── 📁 jira/                          # JIRA相关脚本
│   ├── jira-content.js               # JIRA页面内容脚本
│   ├── jiraDataExtractor.js          # JIRA数据提取器
│   └── jiraStatusMonitor.js          # JIRA状态监控
├── 📁 gitlab/                        # GitLab相关脚本
│   ├── gitlab-content.js             # GitLab页面内容脚本
│   ├── gitlabDataExtractor.js        # GitLab数据提取器
│   └── mrStatusMonitor.js            # MR状态监控
├── 📁 arc/                           # ARC相关脚本
│   ├── arc-content.js                # ARC页面内容脚本
│   └── arcDataHandler.js             # ARC数据处理器
├── 📁 paas/                          # PAAS相关脚本
│   ├── paas-content.js               # PAAS页面内容脚本
│   └── paasNavigator.js              # PAAS导航器
└── 📁 common/                        # 通用脚本
    ├── contentBase.js                # 内容脚本基类
    ├── domUtils.js                   # DOM工具
    └── messageProxy.js               # 消息代理
```

#### 1.3 弹窗界面 (`src/popup/`)
```
src/popup/
├── index.jsx                         # 弹窗入口
├── PopupApp.jsx                      # 主应用组件
├── popup.css                         # 弹窗样式
├── 📁 components/                    # 弹窗组件
│   ├── 📁 TaskManager/               # 任务管理
│   │   ├── TaskList.jsx              # 任务列表
│   │   ├── TaskItem.jsx              # 任务项
│   │   ├── TaskForm.jsx              # 任务表单
│   │   └── TaskFilter.jsx            # 任务筛选
│   ├── 📁 Dashboard/                 # 仪表板
│   │   ├── StatusOverview.jsx        # 状态概览
│   │   ├── RecentActivity.jsx        # 最近活动
│   │   └── QuickActions.jsx          # 快速操作
│   ├── 📁 Notifications/             # 通知中心
│   │   ├── NotificationList.jsx      # 通知列表
│   │   ├── NotificationItem.jsx      # 通知项
│   │   └── NotificationSettings.jsx  # 通知设置
│   └── 📁 Communication/             # 通信功能
│       ├── MessageCenter.jsx         # 消息中心
│       ├── UserSelector.jsx          # 用户选择器
│       └── MessageComposer.jsx       # 消息编辑器
├── 📁 hooks/                         # React Hooks
│   ├── useTask.js                    # 任务相关Hook
│   ├── useNotification.js            # 通知相关Hook
│   └── useStorage.js                 # 存储相关Hook
└── 📁 styles/                        # 样式文件
    ├── components.css                # 组件样式
    ├── layout.css                    # 布局样式
    └── themes.css                    # 主题样式
```

#### 1.4 设置页面 (`src/options/`)
```
src/options/
├── index.jsx                         # 设置页面入口
├── OptionsApp.jsx                    # 设置主应用
├── options.css                       # 设置页面样式
├── 📁 components/                    # 设置组件
│   ├── 📁 General/                   # 通用设置
│   │   ├── GeneralSettings.jsx       # 通用设置面板
│   │   └── UserProfile.jsx           # 用户配置
│   ├── 📁 Integration/               # 集成设置
│   │   ├── JiraConfig.jsx            # JIRA配置
│   │   ├── GitlabConfig.jsx          # GitLab配置
│   │   ├── ArcConfig.jsx             # ARC配置
│   │   └── PaasConfig.jsx            # PAAS配置
│   ├── 📁 Notifications/             # 通知设置
│   │   ├── ReminderSettings.jsx      # 提醒设置
│   │   └── NotificationRules.jsx     # 通知规则
│   └── 📁 Advanced/                  # 高级设置
│       ├── DataManagement.jsx        # 数据管理
│       ├── PermissionSettings.jsx    # 权限设置
│       └── DebugPanel.jsx            # 调试面板
└── 📁 utils/                         # 设置工具
    ├── configValidator.js            # 配置验证器
    └── settingsExporter.js           # 设置导出器
```

#### 1.5 共享组件 (`src/components/`)
```
src/components/
├── 📁 UI/                            # 基础UI组件
│   ├── Button/
│   │   ├── Button.jsx
│   │   └── Button.css
│   ├── Input/
│   │   ├── Input.jsx
│   │   └── Input.css
│   ├── Modal/
│   │   ├── Modal.jsx
│   │   └── Modal.css
│   ├── Dropdown/
│   │   ├── Dropdown.jsx
│   │   └── Dropdown.css
│   └── Loading/
│       ├── Loading.jsx
│       └── Loading.css
├── 📁 Business/                      # 业务组件
│   ├── TaskCard/
│   │   ├── TaskCard.jsx
│   │   └── TaskCard.css
│   ├── StatusBadge/
│   │   ├── StatusBadge.jsx
│   │   └── StatusBadge.css
│   ├── UserAvatar/
│   │   ├── UserAvatar.jsx
│   │   └── UserAvatar.css
│   └── ProgressBar/
│       ├── ProgressBar.jsx
│       └── ProgressBar.css
└── 📁 Layout/                        # 布局组件
    ├── Header/
    │   ├── Header.jsx
    │   └── Header.css
    ├── Sidebar/
    │   ├── Sidebar.jsx
    │   └── Sidebar.css
    └── Container/
        ├── Container.jsx
        └── Container.css
```

#### 1.6 业务服务层 (`src/services/`)
```
src/services/
├── 📁 api/                           # API服务
│   ├── baseApi.js                    # 基础API类
│   ├── jiraApi.js                    # JIRA API
│   ├── gitlabApi.js                  # GitLab API
│   ├── arcApi.js                     # ARC API
│   └── paasApi.js                    # PAAS API
├── 📁 data/                          # 数据服务
│   ├── taskDataService.js            # 任务数据服务
│   ├── userDataService.js            # 用户数据服务
│   ├── notificationDataService.js    # 通知数据服务
│   └── configDataService.js          # 配置数据服务
├── 📁 integration/                   # 集成服务
│   ├── workflowIntegration.js        # 工作流集成
│   ├── dataSync.js                   # 数据同步
│   └── statusMonitor.js              # 状态监控
├── 📁 communication/                 # 通信服务
│   ├── messageService.js             # 消息服务
│   ├── notificationService.js        # 通知服务
│   └── userMentionService.js         # 用户@服务
└── 📁 automation/                    # 自动化服务
    ├── reminderService.js            # 提醒服务
    ├── workflowAutomation.js         # 工作流自动化
    └── dataProcessor.js              # 数据处理服务
```

#### 1.7 工具函数 (`src/utils/`)
```
src/utils/
├── index.js                          # 工具函数入口
├── 📁 chrome/                        # Chrome扩展工具
│   ├── storage.js                    # 存储工具
│   ├── messaging.js                  # 消息传递工具
│   ├── tabs.js                       # 标签页工具
│   └── notifications.js              # 通知工具
├── 📁 data/                          # 数据处理工具
│   ├── validator.js                  # 数据验证
│   ├── formatter.js                  # 数据格式化
│   ├── parser.js                     # 数据解析
│   └── transformer.js                # 数据转换
├── 📁 dom/                           # DOM操作工具
│   ├── selector.js                   # 选择器工具
│   ├── manipulator.js                # DOM操作
│   └── observer.js                   # DOM观察器
├── 📁 time/                          # 时间工具
│   ├── dateUtils.js                  # 日期工具
│   ├── scheduler.js                  # 调度器
│   └── timer.js                      # 定时器
└── 📁 common/                        # 通用工具
    ├── logger.js                     # 日志工具
    ├── debounce.js                   # 防抖工具
    ├── throttle.js                   # 节流工具
    └── constants.js                  # 常量定义
```

#### 1.8 状态管理 (`src/store/`)
```
src/store/
├── index.js                          # Store入口
├── 📁 slices/                        # Redux Slices
│   ├── taskSlice.js                  # 任务状态
│   ├── userSlice.js                  # 用户状态
│   ├── notificationSlice.js          # 通知状态
│   ├── configSlice.js                # 配置状态
│   └── integrationSlice.js           # 集成状态
├── 📁 middleware/                    # 中间件
│   ├── storageMiddleware.js          # 存储中间件
│   ├── syncMiddleware.js             # 同步中间件
│   └── loggerMiddleware.js           # 日志中间件
└── 📁 selectors/                     # 选择器
    ├── taskSelectors.js              # 任务选择器
    ├── userSelectors.js              # 用户选择器
    └── notificationSelectors.js      # 通知选择器
```

#### 1.9 类型定义 (`src/types/`)
```
src/types/
├── index.ts                          # 类型入口
├── task.ts                           # 任务类型
├── user.ts                           # 用户类型
├── notification.ts                   # 通知类型
├── integration.ts                    # 集成类型
├── api.ts                            # API类型
└── common.ts                         # 通用类型
```

#### 1.10 静态资源 (`src/assets/`)
```
src/assets/
├── 📁 images/                        # 图片资源
│   ├── icons/                        # 图标
│   └── illustrations/                # 插图
├── 📁 fonts/                         # 字体文件
└── 📁 styles/                        # 全局样式
    ├── global.css                    # 全局样式
    ├── variables.css                 # CSS变量
    └── reset.css                     # 样式重置
```

### 2. 公共资源 (`public/`)
```
public/
├── manifest.json                     # 扩展清单文件
├── popup.html                        # 弹窗HTML
├── options.html                      # 设置页面HTML
├── 📁 icons/                         # 扩展图标
│   ├── icon16.svg
│   ├── icon48.svg
│   └── icon128.svg
└── 📁 assets/                        # 静态资源
    └── styles/
        └── content.css               # 内容脚本样式
```

### 3. 文档 (`docs/`)
```
docs/
├── README.md                         # 项目说明
├── ARCHITECTURE.md                   # 架构文档
├── API.md                            # API文档
├── DEPLOYMENT.md                     # 部署文档
├── CONTRIBUTING.md                   # 贡献指南
└── 📁 guides/                        # 使用指南
    ├── user-guide.md                 # 用户指南
    ├── developer-guide.md            # 开发者指南
    └── integration-guide.md          # 集成指南
```

### 4. 测试文件 (`tests/`)
```
tests/
├── 📁 unit/                          # 单元测试
│   ├── services/
│   ├── components/
│   └── utils/
├── 📁 integration/                   # 集成测试
│   ├── api/
│   └── workflows/
├── 📁 e2e/                           # 端到端测试
│   ├── popup/
│   ├── options/
│   └── content-scripts/
├── 📁 fixtures/                      # 测试数据
│   ├── mockData.js
│   └── testData.json
└── 📁 helpers/                       # 测试辅助
    ├── testUtils.js
    └── mockHelpers.js
```

### 5. 配置文件
```
项目根目录/
├── package.json                      # 项目配置
├── webpack.config.js                 # Webpack配置
├── .babelrc                          # Babel配置
├── .gitignore                        # Git忽略文件
├── .eslintrc.js                      # ESLint配置
├── .prettierrc                       # Prettier配置
├── tsconfig.json                     # TypeScript配置
├── jest.config.js                    # Jest测试配置
└── .env.example                      # 环境变量示例
```

## 架构设计原则

### 1. 模块化设计
- 每个功能模块独立开发和维护
- 清晰的模块边界和接口定义
- 支持按需加载和懒加载

### 2. 分层架构
- **表现层**: React组件和UI界面
- **业务层**: 服务类和业务逻辑
- **数据层**: 数据存储和API调用
- **工具层**: 通用工具和辅助函数

### 3. 事件驱动
- 基于Chrome扩展的消息传递机制
- 组件间通过事件进行通信
- 支持异步事件处理

### 4. 可扩展性
- 插件化的集成接口
- 配置驱动的功能开关
- 支持第三方扩展

### 5. 数据一致性
- 统一的状态管理
- 数据同步机制
- 缓存策略

## 技术栈选择

### 前端技术
- **React 18**: 用户界面开发
- **Redux Toolkit**: 状态管理
- **TypeScript**: 类型安全
- **CSS Modules**: 样式管理

### 构建工具
- **Webpack 5**: 模块打包
- **Babel**: JavaScript编译
- **ESLint**: 代码检查
- **Prettier**: 代码格式化

### 测试工具
- **Jest**: 单元测试
- **React Testing Library**: 组件测试
- **Puppeteer**: 端到端测试

### Chrome扩展
- **Manifest V3**: 扩展清单
- **Service Worker**: 后台脚本
- **Content Scripts**: 页面脚本
- **Chrome APIs**: 扩展API

## 开发流程

### 1. 开发环境搭建
```bash
# 安装依赖
npm install

# 开发模式
npm run dev

# 构建生产版本
npm run build

# 运行测试
npm run test
```

### 2. 代码规范
- 使用ESLint和Prettier保证代码质量
- 遵循React和TypeScript最佳实践
- 统一的命名规范和文件组织

### 3. 版本控制
- Git Flow工作流
- 语义化版本控制
- 自动化发布流程

### 4. 质量保证
- 单元测试覆盖率 > 80%
- 集成测试和端到端测试
- 代码审查和持续集成

## 部署和发布

### 1. 构建流程
- 自动化构建脚本
- 资源优化和压缩
- 多环境配置支持

### 2. 发布流程
- Chrome Web Store发布
- 版本管理和回滚
- 用户反馈收集

### 3. 监控和维护
- 错误监控和日志收集
- 性能监控和优化
- 用户使用数据分析

---

这个项目结构设计充分考虑了Chrome扩展的特殊性，同时保持了良好的可维护性和可扩展性。每个模块都有明确的职责，便于团队协作开发。