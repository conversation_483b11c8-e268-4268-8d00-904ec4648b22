# SuperDutyRobot 简化开发计划

## 项目目标
基于Chrome扩展的简化版发版管理工具，专注解决核心痛点：
- ✅ 统一的任务管理界面
- ✅ 基础的JIRA数据获取
- ✅ 简单的通知提醒功能
- ✅ 临时发版信息快速生成

---

## AI开发任务清单 (按执行顺序)

### 🔧 阶段1: 基础设施搭建

#### 任务1.1: 更新项目配置 ✅
**目标**: 配置现代化的Chrome扩展基础结构
**具体要求**:
- [x] 更新 `manifest.json` 到 Manifest V3 规范
- [x] 配置必要的权限: `storage`, `notifications`, `activeTab`, `scripting`
- [x] 设置 `background.js` 为 service worker
- [x] 确保 webpack 配置支持新的结构

**输出文件**: `manifest.json`, `webpack.config.js`
**验收标准**: 扩展可以正常加载，无控制台错误

#### 任务1.2: 创建数据存储模块 ✅
**目标**: 建立统一的数据存储和管理系统
**具体要求**:
- [x] 创建 `src/utils/storage.js` - Chrome Storage API封装
- [x] 实现基础的 CRUD 操作 (create, read, update, delete)
- [x] 定义数据结构: 任务(tasks)、设置(settings)、通知(notifications)
- [x] 添加数据验证和错误处理

**输出文件**: `src/utils/storage.js`
**验收标准**: 可以存储和读取JSON数据，有完整的错误处理

#### 任务1.3: 创建通知服务 ✅
**目标**: 实现Chrome通知功能
**具体要求**:
- [x] 创建 `src/utils/notifications.js`
- [x] 封装 Chrome Notifications API
- [x] 支持基础通知类型: 文本通知、进度通知
- [x] 实现通知点击处理

**输出文件**: `src/utils/notifications.js`
**验收标准**: 可以发送和接收通知，点击通知有响应

### 🎨 阶段2: 用户界面开发

#### 任务2.1: 重构Popup界面 ✅
**目标**: 创建简洁的任务管理主界面
**具体要求**:
- [x] 重构 `src/popup/PopupApp.jsx`
- [x] 创建任务列表组件 `src/popup/components/TaskList.jsx`
- [x] 创建任务创建表单 `src/popup/components/TaskForm.jsx`
- [x] 实现基础的任务状态切换 (待办/进行中/完成)
- [x] 添加简单的搜索和筛选功能

**输出文件**: `PopupApp.jsx`, `TaskList.jsx`, `TaskForm.jsx`
**验收标准**: 界面美观，可以创建、查看、编辑任务

#### 任务2.2: 创建设置页面 ✅
**目标**: 提供基础配置功能
**具体要求**:
- [x] 重构 `src/options/OptionsApp.jsx`
- [x] 添加JIRA配置项 (URL, 用户信息)
- [x] 添加通知设置 (开启/关闭, 提醒间隔)
- [x] 添加GitLab配置项 (Token, 项目ID)
- [x] 实现配置的保存和加载

**输出文件**: `OptionsApp.jsx`
**验收标准**: 可以保存和读取配置，界面友好

### 📊 阶段3: 数据集成

#### 任务3.1: JIRA数据抓取 ✅
**目标**: 从JIRA页面获取需求信息
**具体要求**:
- [x] 更新 `src/content-scripts/jira-content.js`
- [x] 实现页面数据解析 (需求号、标题、状态、负责人)
- [x] 添加数据提取的触发机制 (按钮点击或自动检测)
- [x] 实现与popup的消息通信
- [x] 添加错误处理和重试机制

**输出文件**: `jira-content.js`
**验收标准**: 可以从JIRA页面提取需求信息并传递给扩展

**完成内容**:
- 增强了JIRA数据提取功能，支持多种JIRA版本和布局
- 添加了更多CSS选择器以提高数据提取的准确性
- 实现了页面浮动提取按钮，用户可直接点击提取数据
- 添加了错误处理和页面通知功能
- 支持三种提取模式：当前页面、过滤器页面和选中的问题

#### 任务3.2: GitLab基础集成 ✅
**目标**: 实现GitLab API基础调用
**具体要求**:
- [x] 创建 `src/utils/gitlab.js`
- [x] 实现API认证和基础请求封装
- [x] 添加获取项目信息的功能
- [x] 添加获取MR信息的功能
- [x] 实现简单的错误处理和重试

**输出文件**: `src/utils/gitlab.js`
**验收标准**: 可以调用GitLab API获取基础信息

**完成内容**:
- 创建了GitLab API服务类，提供统一的API调用接口
- 实现了API认证和请求封装，支持Bearer Token认证
- 添加了获取项目信息、合并请求和分支信息的功能
- 实现了请求超时处理、错误处理和自动重试机制
- 集成到项目的utils模块，可通过index.js导入使用

### 🚀 阶段4: 核心功能实现

#### 任务4.1: 任务管理系统 ✅
**目标**: 完整的任务CRUD功能
**具体要求**:
- [x] 创建 `src/utils/taskManager.js`
- [x] 实现任务的创建、更新、删除、查询
- [x] 添加任务状态管理 (待办、进行中、完成、取消)
- [x] 实现任务的优先级和标签功能
- [x] 添加任务的时间跟踪 (创建时间、更新时间、截止时间)

**输出文件**: `src/utils/taskManager.js`
**验收标准**: 完整的任务管理功能，数据持久化

**完成内容**:
- 创建了TaskManager类，提供完整的任务管理功能
- 实现了任务的CRUD操作，基于现有的storage.js
- 添加了任务状态和优先级的枚举及管理方法
- 实现了任务标签的添加和移除功能
- 添加了任务截止日期设置和时间跟踪功能
- 提供了任务筛选、搜索和统计功能
- 实现了批量操作功能，如批量更新状态、优先级等

#### 任务4.2: 定时提醒功能 ✅
**目标**: 基于任务的提醒系统
**具体要求**:
- [x] 创建 `src/background/services/reminderService.js`
- [x] 实现基于Chrome Alarms API的定时器
- [x] 添加任务截止时间提醒
- [x] 实现自定义提醒规则
- [x] 添加提醒的暂停和恢复功能

**输出文件**: `src/background/services/reminderService.js`
**验收标准**: 可以按时发送提醒通知

**完成内容**:
- 创建了ReminderService类，提供完整的任务提醒功能
- 实现了基于Chrome Alarms API的定时检查机制
- 添加了即将到期和已逾期任务的自动检查和提醒
- 实现了自定义提醒功能，支持为特定任务设置提醒时间
- 添加了免打扰时间设置，避免在特定时间段发送通知
- 实现了提醒的暂停和恢复功能
- 集成到background.js中，提供完整的消息处理机制

#### 任务4.3: 临时发版工具 ✅
**目标**: 快速生成发版信息
**具体要求**:
- [x] 创建 `src/popup/components/ReleaseGenerator.jsx`
- [x] 实现发版信息模板 (需求号、MR链接、负责人)
- [x] 添加@人员功能 (预设人员列表)
- [x] 实现一键复制功能
- [ ] 添加历史记录功能

**输出文件**: `ReleaseGenerator.jsx`
**验收标准**: 可以快速生成标准化的发版信息

**完成内容**:
- 创建了 `ReleaseGenerator.jsx` 组件，实现了发版信息模板的生成。
- 支持输入JIRA需求号、GitLab MR链接和负责人。
- 实现了预设人员列表的添加、选择和删除功能，并持久化存储。
- 添加了一键复制功能，可将生成的发版信息复制到剪贴板。
- 界面样式已适配项目现有风格。

### 🔗 阶段5: 系统整合

#### 任务5.1: 数据流整合 ✅
**目标**: 连接各个模块，实现数据流转
**具体要求**:
- [x] 更新 `src/background/background.js`
- [x] 实现消息路由和处理
- [x] 添加数据同步机制
- [x] 实现错误收集和日志记录
- [x] 添加性能监控

**输出文件**: `background.js`
**验收标准**: 各模块间数据流转正常，无内存泄漏

#### 任务5.2: 用户体验优化 ✅
**目标**: 提升整体使用体验
**具体要求**:
- [x] 添加加载状态和进度指示
- [x] 实现友好的错误提示
- [x] 添加操作确认和撤销功能
- [x] 优化界面响应速度
- [x] 添加键盘快捷键支持

**输出文件**: 多个组件文件的更新
**验收标准**: 界面流畅，操作直观，错误处理完善

---

## 🎯 MVP功能范围

### 必须实现 (MVP)
1. ✅ 基础任务管理 (创建、查看、完成)
2. ✅ JIRA数据抓取 (手动触发)
3. ✅ 简单通知提醒
4. ✅ 临时发版信息生成
5. ✅ 基础设置配置

### 可选实现 (V1.1)
1. 🔄 自动数据同步
2. 🔄 高级提醒规则
3. 🔄 GitLab深度集成
4. 🔄 数据导出功能

### 暂不实现
1. ❌ 复杂的工作流自动化
2. ❌ 多人协作功能
3. ❌ 高级数据分析
4. ❌ 第三方系统深度集成

---

## 📋 AI开发指南

### 每个任务的标准流程
1. **理解需求**: 仔细阅读任务描述和验收标准
2. **查看现有代码**: 了解当前项目结构和代码风格
3. **设计方案**: 确定实现方法和技术选型
4. **编写代码**: 实现功能，遵循最佳实践
5. **测试验证**: 确保功能正常，满足验收标准
6. **文档更新**: 更新相关注释和文档

### 代码质量要求
- 🔍 **可读性**: 清晰的变量命名，适当的注释
- 🛡️ **健壮性**: 完善的错误处理，边界条件检查
- 🎨 **一致性**: 遵循项目现有的代码风格
- 📦 **模块化**: 合理的代码组织，低耦合高内聚
- ⚡ **性能**: 避免不必要的计算和内存占用

### 优先级说明
- 🔴 **P0**: 核心功能，必须实现
- 🟡 **P1**: 重要功能，尽量实现
- 🟢 **P2**: 增强功能，时间允许时实现

---

**开发建议**: 建议按照任务顺序逐个完成，每完成一个任务就进行测试验证，确保功能正常后再进行下一个任务。这样可以及时发现问题，避免后期大量返工。