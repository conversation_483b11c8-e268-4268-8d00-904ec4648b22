<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>DutyRobot</title>
  <link rel="stylesheet" href="popup.css">
</head>
<body>
  <div class="container">
    <header class="header">
      <h1>🤖 值班萝卜</h1>
      <p class="subtitle">浏览器扩展版</p>
    </header>

    <nav class="nav-tabs">
      <button class="tab-btn active" data-tab="extract">数据提取</button>
      <button class="tab-btn" data-tab="tools">工具箱</button>
      <button class="tab-btn" data-tab="settings">设置</button>
    </nav>

    <!-- 数据提取标签页 -->
    <div class="tab-content active" id="extract-tab">
      <div class="section">
        <h3>JIRA数据提取</h3>
        <div class="status-info" id="jira-status">
          <span class="status-dot"></span>
          <span class="status-text">检查当前页面...</span>
        </div>
        
        <div class="action-buttons">
          <button class="btn btn-primary" id="extract-current">
            <span class="btn-icon">📊</span>
            提取当前页面数据
          </button>
          <button class="btn btn-secondary" id="extract-filter" disabled>
            <span class="btn-icon">🔍</span>
            提取筛选器数据
          </button>
        </div>

        <div class="extracted-data" id="extracted-data" style="display: none;">
          <h4>提取结果 <span id="data-count"></span></h4>
          <div class="data-preview" id="data-preview"></div>
          <div class="export-options">
            <button class="btn btn-small" id="copy-csv">复制CSV</button>
            <button class="btn btn-small" id="copy-json">复制JSON</button>
            <button class="btn btn-small" id="download-csv">下载CSV</button>
          </div>
        </div>
      </div>
    </div>

    <!-- 工具箱标签页 -->
    <div class="tab-content" id="tools-tab">
      <div class="section">
        <h3>自动化工具</h3>
        
        <div class="tool-item">
          <div class="tool-info">
            <h4>自动填表</h4>
            <p>基于JIRA数据自动填写发版表</p>
          </div>
          <button class="btn btn-primary" id="auto-table">
            <span class="btn-icon">📝</span>
            启动
          </button>
        </div>

        <div class="tool-item">
          <div class="tool-info">
            <h4>分支管理</h4>
            <p>批量创建pre-master分支</p>
          </div>
          <button class="btn btn-primary" id="create-branches">
            <span class="btn-icon">🌿</span>
            启动
          </button>
        </div>

        <div class="tool-item">
          <div class="tool-info">
            <h4>MR创建</h4>
            <p>批量创建合并请求</p>
          </div>
          <button class="btn btn-primary" id="create-mr">
            <span class="btn-icon">🔀</span>
            启动
          </button>
        </div>

        <div class="tool-item">
          <div class="tool-info">
            <h4>组合工具</h4>
            <p>一键完成填表+分支+MR</p>
          </div>
          <button class="btn btn-success" id="combo-tool">
            <span class="btn-icon">⚡</span>
            一键执行
          </button>
        </div>
      </div>
    </div>

    <!-- 设置标签页 -->
    <div class="tab-content" id="settings-tab">
      <div class="section">
        <h3>API配置</h3>
        
        <div class="form-group">
          <label for="region-select">地区选择</label>
          <select id="region-select" class="form-control">
            <option value="cn">全球 (CN)</option>
            <option value="wi">Weber (WI)</option>
            <option value="overseas">海外 (Overseas)</option>
          </select>
        </div>



        <div class="form-group">
          <label for="gitlab-token">GitLab访问令牌</label>
          <input type="password" id="gitlab-token" class="form-control" placeholder="输入GitLab访问令牌">
          <small class="form-text">在GitLab个人设置中生成访问令牌</small>
        </div>

        <div class="form-actions">
          <button class="btn btn-primary" id="save-settings">
            <span class="btn-icon">💾</span>
            保存设置
          </button>
          <button class="btn btn-secondary" id="test-connection">
            <span class="btn-icon">🔗</span>
            测试连接
          </button>
        </div>

        <div class="connection-status" id="connection-status" style="display: none;">
          <div class="status-item">
            <span class="status-label">GitLab:</span>
            <span class="status-value" id="gitlab-connection-status">未测试</span>
          </div>
        </div>
      </div>
    </div>

    <footer class="footer">
      <div class="footer-links">
        <a href="#" id="open-options">高级选项</a>
        <a href="#" id="view-help">帮助文档</a>
      </div>
      <div class="version">v1.0.0</div>
    </footer>
  </div>

  <script src="popup.js"></script>
</body>
</html>