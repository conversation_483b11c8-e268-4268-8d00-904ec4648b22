/* DutyRobot Extension Popup Styles */

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  font-size: 14px;
  line-height: 1.5;
  color: #333;
  background: #f8f9fa;
  width: 400px;
  min-height: 500px;
}

.container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  max-height: 600px;
}

/* Header */
.header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 20px;
  text-align: center;
  position: relative;
}

.header h1 {
  font-size: 20px;
  font-weight: 600;
  margin-bottom: 4px;
}

.subtitle {
  font-size: 12px;
  opacity: 0.9;
}

.dashboard-link {
  position: absolute;
  right: 15px;
  top: 15px;
  background-color: rgba(255, 255, 255, 0.2);
  color: white;
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: 6px;
  padding: 8px 12px;
  font-size: 0.75rem;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 4px;
  transition: all 0.2s;
  backdrop-filter: blur(10px);
  z-index: 10;
}

.dashboard-link:hover {
  background-color: rgba(255, 255, 255, 0.3);
  border-color: rgba(255, 255, 255, 0.5);
  transform: translateY(-1px);
}

/* Navigation Tabs */
.nav-tabs {
  display: flex;
  background: white;
  border-bottom: 1px solid #e9ecef;
}

.tab-btn {
  flex: 1;
  padding: 12px 8px;
  border: none;
  background: transparent;
  cursor: pointer;
  font-size: 13px;
  font-weight: 500;
  color: #6c757d;
  transition: all 0.2s ease;
  border-bottom: 2px solid transparent;
}

.tab-btn:hover {
  background: #f8f9fa;
  color: #495057;
}

.tab-btn.active {
  color: #667eea;
  border-bottom-color: #667eea;
  background: white;
}

/* Tab Content */
.tab-content {
  display: none;
  flex: 1;
  padding: 20px;
  overflow-y: auto;
}

.tab-content.active {
  display: block;
}

/* Sections */
.section {
  margin-bottom: 24px;
}

.section h3 {
  font-size: 16px;
  font-weight: 600;
  margin-bottom: 16px;
  color: #495057;
}

.section h4 {
  font-size: 14px;
  font-weight: 600;
  margin-bottom: 12px;
  color: #495057;
}

/* Status Info */
.status-info {
  display: flex;
  align-items: center;
  padding: 12px;
  background: white;
  border-radius: 6px;
  border: 1px solid #e9ecef;
  margin-bottom: 16px;
}

.status-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: #28a745;
  margin-right: 8px;
  animation: pulse 2s infinite;
}

.status-dot.warning {
  background: #ffc107;
}

.status-dot.error {
  background: #dc3545;
}

@keyframes pulse {
  0% { opacity: 1; }
  50% { opacity: 0.5; }
  100% { opacity: 1; }
}

.status-text {
  font-size: 13px;
  color: #6c757d;
}

/* Buttons */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 10px 16px;
  border: none;
  border-radius: 6px;
  font-size: 13px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  text-decoration: none;
  gap: 6px;
}

.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.btn-primary {
  background: #667eea;
  color: white;
}

.btn-primary:hover:not(:disabled) {
  background: #5a6fd8;
  transform: translateY(-1px);
}

.btn-secondary {
  background: #6c757d;
  color: white;
}

.btn-secondary:hover:not(:disabled) {
  background: #5a6268;
}

.btn-success {
  background: #28a745;
  color: white;
}

.btn-success:hover:not(:disabled) {
  background: #218838;
}

.btn-small {
  padding: 6px 12px;
  font-size: 12px;
}

.btn-icon {
  font-size: 14px;
}

/* Action Buttons */
.action-buttons {
  display: flex;
  flex-direction: column;
  gap: 8px;
  margin-bottom: 20px;
}

.action-buttons .btn {
  width: 100%;
}

/* Tool Items */
.tool-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px;
  background: white;
  border-radius: 8px;
  border: 1px solid #e9ecef;
  margin-bottom: 12px;
  transition: all 0.2s ease;
}

.tool-item:hover {
  border-color: #667eea;
  box-shadow: 0 2px 8px rgba(102, 126, 234, 0.1);
}

.tool-info h4 {
  font-size: 14px;
  font-weight: 600;
  margin-bottom: 4px;
  color: #495057;
}

.tool-info p {
  font-size: 12px;
  color: #6c757d;
  margin: 0;
}

/* Forms */
.form-group {
  margin-bottom: 16px;
}

.form-group label {
  display: block;
  font-size: 13px;
  font-weight: 500;
  margin-bottom: 6px;
  color: #495057;
}

.form-control {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid #ced4da;
  border-radius: 4px;
  font-size: 13px;
  transition: border-color 0.2s ease;
}

.form-control:focus {
  outline: none;
  border-color: #667eea;
  box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.2);
}

.form-text {
  font-size: 11px;
  color: #6c757d;
  margin-top: 4px;
}

.form-actions {
  display: flex;
  gap: 8px;
  margin-bottom: 16px;
}

.form-actions .btn {
  flex: 1;
}

/* Extracted Data */
.extracted-data {
  background: white;
  border-radius: 8px;
  border: 1px solid #e9ecef;
  padding: 16px;
  margin-top: 16px;
}

.data-preview {
  max-height: 200px;
  overflow-y: auto;
  background: #f8f9fa;
  border-radius: 4px;
  padding: 12px;
  margin-bottom: 12px;
  font-family: 'Monaco', 'Menlo', monospace;
  font-size: 11px;
  line-height: 1.4;
}

.export-options {
  display: flex;
  gap: 8px;
}

.export-options .btn {
  flex: 1;
}

/* Connection Status */
.connection-status {
  background: white;
  border-radius: 6px;
  border: 1px solid #e9ecef;
  padding: 12px;
}

.status-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 6px 0;
}

.status-item:not(:last-child) {
  border-bottom: 1px solid #f1f3f4;
}

.status-label {
  font-size: 13px;
  font-weight: 500;
  color: #495057;
}

.status-value {
  font-size: 12px;
  padding: 2px 8px;
  border-radius: 12px;
  background: #e9ecef;
  color: #6c757d;
}

.status-value.success {
  background: #d4edda;
  color: #155724;
}

.status-value.error {
  background: #f8d7da;
  color: #721c24;
}

/* Footer */
.footer {
  padding: 16px 20px;
  background: white;
  border-top: 1px solid #e9ecef;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.footer-links {
  display: flex;
  gap: 12px;
}

.footer-links a {
  font-size: 12px;
  color: #6c757d;
  text-decoration: none;
}

.footer-links a:hover {
  color: #667eea;
}

.version {
  font-size: 11px;
  color: #adb5bd;
}

/* Scrollbar */
::-webkit-scrollbar {
  width: 6px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* Responsive */
@media (max-width: 380px) {
  body {
    width: 350px;
  }
  
  .header {
    padding: 16px;
  }
  
  .tab-content {
    padding: 16px;
  }
}