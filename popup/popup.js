// DutyRobot Extension Popup Script

// DOM元素
const tabButtons = document.querySelectorAll('.tab-btn');
const tabContents = document.querySelectorAll('.tab-content');
const extractCurrentBtn = document.getElementById('extract-current');
const extractFilterBtn = document.getElementById('extract-filter');
const jiraStatus = document.getElementById('jira-status');
const extractedDataDiv = document.getElementById('extracted-data');
const dataPreview = document.getElementById('data-preview');
const dataCount = document.getElementById('data-count');
const saveSettingsBtn = document.getElementById('save-settings');
const testConnectionBtn = document.getElementById('test-connection');
const connectionStatus = document.getElementById('connection-status');

// 当前提取的数据
let currentExtractedData = [];

// 初始化
document.addEventListener('DOMContentLoaded', async () => {
  initializeTabs();
  await loadSettings();
  await checkCurrentPage();
  bindEvents();
  addDashboardLink();
});

// 添加工作台链接
function addDashboardLink() {
  const headerElement = document.querySelector('.header');
  if (headerElement) {
    const dashboardLink = document.createElement('button');
    dashboardLink.className = 'dashboard-link';
    dashboardLink.innerHTML = '<span class="btn-icon">🖥️</span> 控制台';
    dashboardLink.title = '打开任务和设置控制台';
    dashboardLink.addEventListener('click', () => {
      chrome.tabs.create({ url: chrome.runtime.getURL('dashboard.html') });
    });
    headerElement.appendChild(dashboardLink);
  }
}

// 初始化标签页
function initializeTabs() {
  tabButtons.forEach(button => {
    button.addEventListener('click', () => {
      const tabName = button.dataset.tab;
      switchTab(tabName);
    });
  });
}

// 切换标签页
function switchTab(tabName) {
  // 更新按钮状态
  tabButtons.forEach(btn => btn.classList.remove('active'));
  document.querySelector(`[data-tab="${tabName}"]`).classList.add('active');
  
  // 更新内容显示
  tabContents.forEach(content => content.classList.remove('active'));
  document.getElementById(`${tabName}-tab`).classList.add('active');
}

// 加载设置
async function loadSettings() {
  try {
    const settings = await chrome.storage.sync.get([
      'region', 'gitlabAccessToken'
    ]);
    
    if (settings.region) {
      document.getElementById('region-select').value = settings.region;
    }
    
    if (settings.gitlabAccessToken) {
      document.getElementById('gitlab-token').value = settings.gitlabAccessToken;
    }
  } catch (error) {
    console.error('加载设置失败:', error);
  }
}

// 检查当前页面
async function checkCurrentPage() {
  try {
    const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });
    const url = tab.url;
    
    const statusDot = jiraStatus.querySelector('.status-dot');
    const statusText = jiraStatus.querySelector('.status-text');
    
    if (url.includes('jira.dotfashion.cn')) {
      statusDot.className = 'status-dot';
      statusText.textContent = '当前在JIRA页面，可以提取数据';
      extractCurrentBtn.disabled = false;
      
      if (url.includes('/issues/?filter=')) {
        extractFilterBtn.disabled = false;
        statusText.textContent = '检测到JIRA筛选器页面，可以提取列表数据';
      }
    } else {
      statusDot.className = 'status-dot warning';
      statusText.textContent = '请导航到JIRA页面以提取数据';
      extractCurrentBtn.disabled = true;
      extractFilterBtn.disabled = true;
    }
  } catch (error) {
    console.error('检查页面失败:', error);
    const statusDot = jiraStatus.querySelector('.status-dot');
    const statusText = jiraStatus.querySelector('.status-text');
    statusDot.className = 'status-dot error';
    statusText.textContent = '无法检查当前页面';
  }
}

// 绑定事件
function bindEvents() {
  // 数据提取按钮
  extractCurrentBtn.addEventListener('click', extractCurrentPageData);
  extractFilterBtn.addEventListener('click', extractFilterData);
  
  // 导出按钮
  document.getElementById('copy-csv').addEventListener('click', () => copyData('csv'));
  document.getElementById('copy-json').addEventListener('click', () => copyData('json'));
  document.getElementById('download-csv').addEventListener('click', downloadCSV);
  
  // 工具按钮
  document.getElementById('auto-table').addEventListener('click', () => runTool('autoTable'));
  document.getElementById('create-branches').addEventListener('click', () => runTool('createBranches'));
  document.getElementById('create-mr').addEventListener('click', () => runTool('createMR'));
  document.getElementById('combo-tool').addEventListener('click', () => runTool('combo'));
  
  // 设置按钮
  saveSettingsBtn.addEventListener('click', saveSettings);
  testConnectionBtn.addEventListener('click', testConnection);
  
  // 其他链接
  document.getElementById('open-options').addEventListener('click', () => {
    chrome.runtime.openOptionsPage();
  });
  
  document.getElementById('view-help').addEventListener('click', () => {
    chrome.tabs.create({ url: 'https://github.com/your-repo/dutyrobot-extension' });
  });
}

// 提取当前页面数据
async function extractCurrentPageData() {
  try {
    extractCurrentBtn.disabled = true;
    extractCurrentBtn.innerHTML = '<span class="btn-icon">⏳</span>提取中...';
    
    const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });
    
    // 发送消息到content script
    const response = await chrome.tabs.sendMessage(tab.id, { action: 'extractJiraData' });
    
    // 等待数据提取完成
    setTimeout(async () => {
      const result = await chrome.storage.local.get(['extractedIssues', 'extractedIssue']);
      
      if (result.extractedIssues && result.extractedIssues.length > 0) {
        currentExtractedData = result.extractedIssues;
        displayExtractedData(result.extractedIssues);
      } else if (result.extractedIssue) {
        currentExtractedData = [result.extractedIssue];
        displayExtractedData([result.extractedIssue]);
      } else {
        showMessage('未找到数据，请确保页面已完全加载', 'warning');
      }
      
      extractCurrentBtn.disabled = false;
      extractCurrentBtn.innerHTML = '<span class="btn-icon">📊</span>提取当前页面数据';
    }, 2000);
    
  } catch (error) {
    console.error('提取数据失败:', error);
    showMessage('提取数据失败: ' + error.message, 'error');
    extractCurrentBtn.disabled = false;
    extractCurrentBtn.innerHTML = '<span class="btn-icon">📊</span>提取当前页面数据';
  }
}

// 提取筛选器数据
async function extractFilterData() {
  try {
    extractFilterBtn.disabled = true;
    extractFilterBtn.innerHTML = '<span class="btn-icon">⏳</span>提取中...';
    
    const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });
    
    // 发送消息到content script进行页面数据提取
    const response = await chrome.tabs.sendMessage(tab.id, { action: 'extractJiraData' });
    
    // 等待数据提取完成
    setTimeout(async () => {
      const result = await chrome.storage.local.get(['extractedIssues', 'extractedIssue']);
      
      if (result.extractedIssues && result.extractedIssues.length > 0) {
        currentExtractedData = result.extractedIssues;
        displayExtractedData(result.extractedIssues);
      } else if (result.extractedIssue) {
        currentExtractedData = [result.extractedIssue];
        displayExtractedData([result.extractedIssue]);
      } else {
        showMessage('未找到数据，请确保页面已完全加载', 'warning');
      }
    }, 2000);
    
  } catch (error) {
    console.error('提取筛选器数据失败:', error);
    showMessage('提取筛选器数据失败: ' + error.message, 'error');
  } finally {
    extractFilterBtn.disabled = false;
    extractFilterBtn.innerHTML = '<span class="btn-icon">🔍</span>提取筛选器数据';
  }
}

// 显示提取的数据
function displayExtractedData(data) {
  if (!data || data.length === 0) {
    extractedDataDiv.style.display = 'none';
    return;
  }
  
  dataCount.textContent = `(${data.length}条)`;
  
  // 显示数据预览
  const preview = data.slice(0, 5).map(item => 
    `${item.key}: ${item.summary} [${item.status}]`
  ).join('\n');
  
  dataPreview.textContent = preview + (data.length > 5 ? '\n...' : '');
  extractedDataDiv.style.display = 'block';
}

// 复制数据
async function copyData(format) {
  if (!currentExtractedData || currentExtractedData.length === 0) {
    showMessage('没有可复制的数据', 'warning');
    return;
  }
  
  let text = '';
  
  if (format === 'csv') {
    const headers = ['Key', 'Summary', 'Status', 'Assignee', 'Priority', 'URL'];
    text = headers.join(',') + '\n';
    text += currentExtractedData.map(item => 
      [item.key, `"${item.summary}"`, item.status, item.assignee, item.priority, item.url].join(',')
    ).join('\n');
  } else if (format === 'json') {
    text = JSON.stringify(currentExtractedData, null, 2);
  }
  
  try {
    await navigator.clipboard.writeText(text);
    showMessage(`已复制${format.toUpperCase()}格式数据到剪贴板`, 'success');
  } catch (error) {
    console.error('复制失败:', error);
    showMessage('复制失败', 'error');
  }
}

// 下载CSV
function downloadCSV() {
  if (!currentExtractedData || currentExtractedData.length === 0) {
    showMessage('没有可下载的数据', 'warning');
    return;
  }
  
  const headers = ['Key', 'Summary', 'Status', 'Assignee', 'Priority', 'URL'];
  let csv = headers.join(',') + '\n';
  csv += currentExtractedData.map(item => 
    [item.key, `"${item.summary}"`, item.status, item.assignee, item.priority, item.url].join(',')
  ).join('\n');
  
  const blob = new Blob([csv], { type: 'text/csv;charset=utf-8;' });
  const link = document.createElement('a');
  link.href = URL.createObjectURL(blob);
  link.download = `jira-data-${new Date().toISOString().split('T')[0]}.csv`;
  link.click();
  
  showMessage('CSV文件已下载', 'success');
}

// 运行工具
function runTool(toolName) {
  showMessage(`${toolName}功能正在开发中`, 'info');
  // TODO: 实现各种工具功能
}

// 保存设置
async function saveSettings() {
  try {
    const region = document.getElementById('region-select').value;
    const gitlabToken = document.getElementById('gitlab-token').value;
    
    await chrome.storage.sync.set({
      region: region,
      gitlabAccessToken: gitlabToken
    });
    
    showMessage('设置已保存', 'success');
  } catch (error) {
    console.error('保存设置失败:', error);
    showMessage('保存设置失败', 'error');
  }
}

// 测试连接
async function testConnection() {
  testConnectionBtn.disabled = true;
  testConnectionBtn.innerHTML = '<span class="btn-icon">⏳</span>测试中...';
  
  const gitlabStatus = document.getElementById('gitlab-connection-status');
  
  // 测试GitLab连接
  try {
    const gitlabResponse = await chrome.runtime.sendMessage({
      action: 'fetchGitlabData',
      projectId: '424', // 测试项目ID
      endpoint: ''
    });
    
    if (gitlabResponse.success) {
      gitlabStatus.textContent = '连接成功';
      gitlabStatus.className = 'status-value success';
    } else {
      gitlabStatus.textContent = '连接失败';
      gitlabStatus.className = 'status-value error';
    }
  } catch (error) {
    gitlabStatus.textContent = '连接失败';
    gitlabStatus.className = 'status-value error';
  }
  
  connectionStatus.style.display = 'block';
  testConnectionBtn.disabled = false;
  testConnectionBtn.innerHTML = '<span class="btn-icon">🔗</span>测试连接';
}

// 显示消息
function showMessage(message, type = 'info') {
  // 创建临时消息元素
  const messageDiv = document.createElement('div');
  messageDiv.style.cssText = `
    position: fixed;
    top: 10px;
    left: 50%;
    transform: translateX(-50%);
    padding: 8px 16px;
    border-radius: 4px;
    color: white;
    font-size: 12px;
    z-index: 10000;
    max-width: 300px;
    text-align: center;
  `;
  
  switch (type) {
    case 'success':
      messageDiv.style.backgroundColor = '#28a745';
      break;
    case 'warning':
      messageDiv.style.backgroundColor = '#ffc107';
      messageDiv.style.color = '#212529';
      break;
    case 'error':
      messageDiv.style.backgroundColor = '#dc3545';
      break;
    default:
      messageDiv.style.backgroundColor = '#17a2b8';
  }
  
  messageDiv.textContent = message;
  document.body.appendChild(messageDiv);
  
  setTimeout(() => {
    if (messageDiv.parentNode) {
      messageDiv.parentNode.removeChild(messageDiv);
    }
  }, 3000);
}