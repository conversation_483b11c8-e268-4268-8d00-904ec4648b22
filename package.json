{"name": "dutyrobot-browser-extension", "version": "1.0.0", "description": "值班机器人浏览器扩展，基于原始dutyRobot项目改造，用于获取JIRA数据和自动化值班任务", "main": "background.js", "scripts": {"build": "webpack --mode=production", "dev": "webpack --mode=development --watch", "start": "webpack --mode=development --watch", "lint": "echo 'Linting JavaScript files...' && find . -name '*.js' -not -path './node_modules/*' | head -5", "test": "echo 'No tests specified yet'", "package": "pnpm run build && zip -r dutyrobot-extension.zip dist/", "clean": "rm -rf dist/ dutyrobot-extension.zip"}, "keywords": ["chrome-extension", "jira", "gitlab", "automation", "duty-robot", "browser-extension", "data-extraction"], "author": {"name": "DutyRobot Team", "email": "<EMAIL>"}, "license": "MIT", "repository": {"type": "git", "url": "https://github.com/your-org/dutyrobot-extension.git"}, "bugs": {"url": "https://github.com/your-org/dutyrobot-extension/issues"}, "homepage": "https://github.com/your-org/dutyrobot-extension#readme", "engines": {"node": ">=14.0.0", "pnpm": ">=7.0.0"}, "dependencies": {"prop-types": "^15.8.1", "react": "^18.2.0", "react-dom": "^18.2.0"}, "devDependencies": {"@babel/core": "^7.23.0", "@babel/preset-env": "^7.23.0", "@babel/preset-react": "^7.22.0", "@types/chrome": "^0.0.246", "babel-loader": "^9.1.0", "copy-webpack-plugin": "^11.0.0", "css-loader": "^6.8.0", "html-webpack-plugin": "^5.5.0", "style-loader": "^3.3.0", "webpack": "^5.89.0", "webpack-cli": "^5.1.0"}, "extensionInfo": {"manifestVersion": 3, "targetBrowsers": ["chrome", "edge", "firefox"], "permissions": ["activeTab", "storage", "scripting", "contextMenus"], "hostPermissions": ["https://jira.dotfashion.cn/*", "https://gitlab.sheincorp.cn/*"]}, "originalProject": {"name": "dutyRobot", "description": "值班萝卜 - 集成一些日常值班的辅助工具", "features": ["autoTable - 自动填写前端发版表", "createPreMaster - 批量创建pre-master分支", "filterTag - 文本处理和标签过滤", "createMR - 批量创建合并请求", "combo - 组合工具一键执行"]}, "extensionFeatures": {"dataExtraction": {"description": "从JIRA页面提取问题数据", "supportedPages": ["问题列表页面", "筛选器结果页面", "单个问题详情页面"], "exportFormats": ["CSV", "JSON", "Excel"]}, "automation": {"description": "自动化值班任务", "tools": ["自动填表", "分支管理", "MR创建", "组合工具"]}, "configuration": {"description": "灵活的配置选项", "features": ["多地区支持", "项目映射配置", "API令牌管理", "导出设置"]}}}