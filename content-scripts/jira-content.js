// JIRA页面内容脚本

// 监听来自background script的消息
chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
  if (request.action === 'extractJiraData') {
    extractJiraData();
  }
});

// 提取JIRA数据
function extractJiraData() {
  try {
    const currentUrl = window.location.href;
    
    // 检查是否在JIRA问题列表页面
    if (currentUrl.includes('/issues/?filter=')) {
      extractIssueListData();
    } else if (currentUrl.includes('/browse/')) {
      extractSingleIssueData();
    } else {
      showNotification('当前页面不支持数据提取', 'warning');
    }
  } catch (error) {
    console.error('提取JIRA数据时出错:', error);
    showNotification('提取数据时出错: ' + error.message, 'error');
  }
}

// 提取问题列表数据
function extractIssueListData() {
  const issues = [];
  
  // 查找问题列表容器
  const issueRows = document.querySelectorAll('.issue-list .issuerow, .navigator-content .issue-list tr');
  
  if (issueRows.length === 0) {
    // 尝试新版JIRA界面
    const newIssueRows = document.querySelectorAll('[data-testid="issue.views.issue-base.foundation.summary.heading"]');
    
    if (newIssueRows.length === 0) {
      showNotification('未找到问题列表，请确保页面已完全加载', 'warning');
      return;
    }
    
    // 处理新版界面
    newIssueRows.forEach(row => {
      const issue = extractNewFormatIssue(row);
      if (issue) issues.push(issue);
    });
  } else {
    // 处理旧版界面
    issueRows.forEach(row => {
      const issue = extractOldFormatIssue(row);
      if (issue) issues.push(issue);
    });
  }
  
  if (issues.length > 0) {
    // 发送数据到popup或存储
    chrome.storage.local.set({ extractedIssues: issues }, () => {
      showNotification(`成功提取 ${issues.length} 个问题`, 'success');
      
      // 可选：自动打开popup显示结果
      chrome.runtime.sendMessage({ action: 'showExtractedData', data: issues });
    });
  } else {
    showNotification('未找到有效的问题数据', 'warning');
  }
}

// 提取新版JIRA格式的问题
function extractNewFormatIssue(element) {
  try {
    const issueContainer = element.closest('[data-testid*="issue"]');
    
    const key = issueContainer?.querySelector('[data-testid*="issue-key"]')?.textContent?.trim();
    const summary = element.textContent?.trim();
    const status = issueContainer?.querySelector('[data-testid*="status"]')?.textContent?.trim();
    const assignee = issueContainer?.querySelector('[data-testid*="assignee"]')?.textContent?.trim();
    const priority = issueContainer?.querySelector('[data-testid*="priority"]')?.getAttribute('aria-label');
    
    return {
      key: key || '',
      summary: summary || '',
      status: status || '',
      assignee: assignee || '',
      priority: priority || '',
      url: `https://jira.dotfashion.cn/browse/${key}`
    };
  } catch (error) {
    console.error('提取新格式问题时出错:', error);
    return null;
  }
}

// 提取旧版JIRA格式的问题
function extractOldFormatIssue(row) {
  try {
    const key = row.querySelector('.issuekey a, .issue-link')?.textContent?.trim();
    const summary = row.querySelector('.summary a, .issue-summary')?.textContent?.trim();
    const status = row.querySelector('.status, .issue-status')?.textContent?.trim();
    const assignee = row.querySelector('.assignee, .issue-assignee')?.textContent?.trim();
    const priority = row.querySelector('.priority, .issue-priority')?.getAttribute('title') || 
                    row.querySelector('.priority, .issue-priority')?.textContent?.trim();
    
    return {
      key: key || '',
      summary: summary || '',
      status: status || '',
      assignee: assignee || '',
      priority: priority || '',
      url: `https://jira.dotfashion.cn/browse/${key}`
    };
  } catch (error) {
    console.error('提取旧格式问题时出错:', error);
    return null;
  }
}

// 提取单个问题详情
function extractSingleIssueData() {
  try {
    const key = document.querySelector('#key-val, [data-testid*="issue-key"]')?.textContent?.trim();
    const summary = document.querySelector('#summary-val, [data-testid*="issue-title"]')?.textContent?.trim();
    const status = document.querySelector('#status-val, [data-testid*="status"]')?.textContent?.trim();
    const assignee = document.querySelector('#assignee-val, [data-testid*="assignee"]')?.textContent?.trim();
    const priority = document.querySelector('#priority-val, [data-testid*="priority"]')?.textContent?.trim();
    const description = document.querySelector('#description-val, [data-testid*="description"]')?.textContent?.trim();
    
    const issue = {
      key: key || '',
      summary: summary || '',
      status: status || '',
      assignee: assignee || '',
      priority: priority || '',
      description: description || '',
      url: window.location.href
    };
    
    chrome.storage.local.set({ extractedIssue: issue }, () => {
      showNotification('成功提取问题详情', 'success');
      chrome.runtime.sendMessage({ action: 'showExtractedData', data: [issue] });
    });
  } catch (error) {
    console.error('提取问题详情时出错:', error);
    showNotification('提取问题详情时出错: ' + error.message, 'error');
  }
}

// 显示通知
function showNotification(message, type = 'info') {
  // 创建通知元素
  const notification = document.createElement('div');
  notification.style.cssText = `
    position: fixed;
    top: 20px;
    right: 20px;
    padding: 12px 20px;
    border-radius: 4px;
    color: white;
    font-weight: bold;
    z-index: 10000;
    max-width: 300px;
    box-shadow: 0 4px 12px rgba(0,0,0,0.3);
    transition: all 0.3s ease;
  `;
  
  // 根据类型设置颜色
  switch (type) {
    case 'success':
      notification.style.backgroundColor = '#4CAF50';
      break;
    case 'warning':
      notification.style.backgroundColor = '#FF9800';
      break;
    case 'error':
      notification.style.backgroundColor = '#F44336';
      break;
    default:
      notification.style.backgroundColor = '#2196F3';
  }
  
  notification.textContent = message;
  document.body.appendChild(notification);
  
  // 3秒后自动移除
  setTimeout(() => {
    if (notification.parentNode) {
      notification.style.opacity = '0';
      setTimeout(() => {
        notification.parentNode.removeChild(notification);
      }, 300);
    }
  }, 3000);
}

// 页面加载完成后添加提取按钮
if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', addExtractButton);
} else {
  addExtractButton();
}

// 添加提取按钮到页面
function addExtractButton() {
  // 避免重复添加
  if (document.getElementById('dutyrobot-extract-btn')) {
    return;
  }
  
  const button = document.createElement('button');
  button.id = 'dutyrobot-extract-btn';
  button.textContent = '提取数据';
  button.style.cssText = `
    position: fixed;
    bottom: 20px;
    right: 20px;
    padding: 10px 15px;
    background-color: #0052CC;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    z-index: 10000;
    font-size: 14px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.2);
  `;
  
  button.addEventListener('click', extractJiraData);
  button.addEventListener('mouseenter', () => {
    button.style.backgroundColor = '#0065FF';
  });
  button.addEventListener('mouseleave', () => {
    button.style.backgroundColor = '#0052CC';
  });
  
  document.body.appendChild(button);
}