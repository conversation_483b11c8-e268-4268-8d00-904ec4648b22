# 🤖 DutyRobot Browser Extension

值班机器人浏览器扩展版本 - 基于原始 dutyRobot 项目改造的浏览器扩展，用于获取 JIRA 数据和自动化值班任务。

## 📋 功能特性

### 🔍 数据提取
- **JIRA 数据提取**: 从 JIRA 页面直接提取问题数据
- **多页面支持**: 支持问题列表、筛选器结果、单个问题详情页面
- **多格式导出**: 支持 CSV、JSON、Excel 格式导出
- **自定义字段**: 可配置提取的字段内容
- **批量处理**: 支持大量数据的批量提取

### 🛠️ 自动化工具
- **自动填表**: 基于 JIRA 数据自动填写发版表
- **分支管理**: 批量创建 pre-master 分支
- **MR 创建**: 批量创建合并请求
- **组合工具**: 一键完成填表+分支+MR 的完整流程

### ⚙️ 配置管理
- **多地区支持**: 支持 CN、WI、Overseas 等不同地区配置
- **项目映射**: 灵活配置 GitLab 项目 ID 映射
- **API 管理**: 安全的 JIRA 和 GitLab 访问令牌管理
- **设置导入导出**: 支持配置的备份和恢复

## 🚀 安装使用

### 安装方式

1. **开发者模式安装**:
   - 打开 Chrome 浏览器
   - 访问 `chrome://extensions/`
   - 开启「开发者模式」
   - 点击「加载已解压的扩展程序」
   - 选择 `dutyRobotNew` 文件夹

2. **打包安装**:
   ```bash
   npm run package
   ```
   然后在扩展管理页面加载生成的 `.zip` 文件

### 初始配置

1. **配置 API 令牌**:
   - 点击扩展图标打开弹窗
   - 切换到「设置」标签页
   - 配置 JIRA 和 GitLab 访问令牌

2. **选择地区**:
   - 根据你的工作地区选择对应配置
   - 系统会自动加载对应的项目映射

3. **测试连接**:
   - 点击「测试连接」确保配置正确
   - 绿色状态表示连接成功

## 📖 使用指南

### 数据提取

1. **在 JIRA 页面提取数据**:
   - 导航到 JIRA 问题列表或筛选器页面
   - 点击扩展图标
   - 在「数据提取」标签页点击「提取当前页面数据」
   - 或者点击页面右下角的「提取数据」按钮

2. **导出数据**:
   - 提取完成后，选择导出格式
   - 点击「复制CSV」、「复制JSON」或「下载CSV」
   - 数据将保存到剪贴板或下载到本地

### 自动化工具

1. **自动填表**:
   - 在「工具箱」标签页点击「自动填表」
   - 系统将基于提取的 JIRA 数据生成发版表

2. **分支管理**:
   - 点击「分支管理」创建 pre-master 分支
   - 支持批量操作多个项目

3. **一键执行**:
   - 点击「一键执行」完成完整的发版流程
   - 包括填表、创建分支、创建 MR

### 高级配置

1. **打开高级选项**:
   - 在弹窗底部点击「高级选项」
   - 或右键扩展图标选择「选项」

2. **项目配置**:
   - 在「项目配置」部分添加或修改项目映射
   - 设置默认选中的项目

3. **数据提取设置**:
   - 配置最大提取数量
   - 选择要提取的字段
   - 设置自动提取选项

## 🔧 开发说明

### 项目结构

```
dutyRobotNew/
├── manifest.json          # 扩展清单文件
├── background.js          # 后台服务工作者
├── content-scripts/       # 内容脚本
│   └── jira-content.js   # JIRA 页面内容脚本
├── popup/                 # 弹窗页面
│   ├── popup.html        # 弹窗 HTML
│   ├── popup.css         # 弹窗样式
│   └── popup.js          # 弹窗逻辑
├── options/               # 选项页面
│   ├── options.html      # 选项页面 HTML
│   ├── options.css       # 选项页面样式
│   └── options.js        # 选项页面逻辑
├── package.json          # 项目配置
└── README.md            # 说明文档
```

### 核心功能

1. **数据提取引擎**:
   - 支持新旧版 JIRA 界面
   - 智能识别页面元素
   - 容错处理和数据清洗

2. **API 集成**:
   - JIRA REST API 集成
   - GitLab API 集成
   - 安全的令牌管理

3. **用户界面**:
   - 现代化的 UI 设计
   - 响应式布局
   - 直观的操作流程

### 开发命令

```bash
# 安装依赖
npm install

# 开发模式
npm run dev

# 构建项目
npm run build

# 打包扩展
npm run package

# 清理文件
npm run clean
```

## 🔒 安全说明

### 权限说明
- `activeTab`: 访问当前活动标签页
- `storage`: 存储配置信息
- `scripting`: 注入内容脚本
- `https://jira.dotfashion.cn/*`: 访问 JIRA 服务器
- `https://gitlab.sheincorp.cn/*`: 访问 GitLab 服务器

### 数据安全
- 所有配置信息存储在本地
- API 令牌加密存储
- 不会向第三方服务器发送数据
- 支持配置导出备份

## 🆚 与原版对比

| 功能 | 原版 dutyRobot | 浏览器扩展版 |
|------|----------------|-------------|
| 运行环境 | Node.js 命令行 | 浏览器扩展 |
| 数据获取 | API 调用 | 页面直接提取 + API |
| 用户界面 | 命令行交互 | 图形化界面 |
| 配置管理 | .env 文件 | 扩展存储 |
| 使用便利性 | 需要命令行操作 | 一键操作 |
| 数据导出 | 文件输出 | 多格式导出 |
| 自动化程度 | 半自动 | 全自动 |

## 🐛 问题反馈

如果遇到问题或有功能建议，请：

1. 检查控制台错误信息
2. 确认 API 令牌配置正确
3. 验证网络连接正常
4. 提交 Issue 到项目仓库

## 📝 更新日志

### v1.0.0 (2024-01-XX)
- 🎉 初始版本发布
- ✨ 支持 JIRA 数据提取
- ✨ 集成原版 dutyRobot 核心功能
- ✨ 现代化用户界面
- ✨ 多地区配置支持
- ✨ 高级配置选项

## 📄 许可证

MIT License - 详见 [LICENSE](LICENSE) 文件

## 🤝 贡献

欢迎提交 Pull Request 或 Issue！

---

**基于原始 dutyRobot 项目改造** | **Made with ❤️ for 值班同学们**