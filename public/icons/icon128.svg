<svg width="128" height="128" viewBox="0 0 128 128" fill="none" xmlns="http://www.w3.org/2000/svg">
  <rect width="128" height="128" rx="20" fill="url(#gradient)"/>
  <defs>
    <linearGradient id="gradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#667eea;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#764ba2;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- Document lines -->
  <rect x="24" y="32" width="80" height="6" rx="3" fill="white" opacity="0.9"/>
  <rect x="24" y="44" width="80" height="6" rx="3" fill="white" opacity="0.8"/>
  <rect x="24" y="56" width="64" height="6" rx="3" fill="white" opacity="0.7"/>
  <rect x="24" y="68" width="72" height="6" rx="3" fill="white" opacity="0.6"/>
  <rect x="24" y="80" width="56" height="6" rx="3" fill="white" opacity="0.5"/>
  
  <!-- Success indicator -->
  <circle cx="96" cy="32" r="16" fill="#28a745"/>
  <path d="M88 32l6 6 10-10" stroke="white" stroke-width="4" stroke-linecap="round" stroke-linejoin="round"/>
  
  <!-- Robot element -->
  <rect x="20" y="96" width="88" height="8" rx="4" fill="white" opacity="0.3"/>
  <text x="64" y="108" text-anchor="middle" fill="white" font-family="Arial, sans-serif" font-size="12" font-weight="bold">DutyRobot</text>
</svg>