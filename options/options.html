<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>DutyRobot - 高级选项</title>
  <link rel="stylesheet" href="options.css">
</head>
<body>
  <div class="container">
    <header class="header">
      <h1>🤖 值班萝卜 - 高级选项</h1>
      <p class="subtitle">配置扩展的详细设置和高级功能</p>
    </header>

    <main class="main-content">
      <div class="settings-grid">
        <!-- API配置 -->
        <section class="settings-section">
          <h2>gitlab API配置</h2>

          <div class="form-group">
            <label for="gitlab-api-url">GitLab API地址</label>
            <input type="url" id="gitlab-api-url" class="form-control" 
                   value="https://gitlab.sheincorp.cn/api/v4" 
                   placeholder="https://gitlab.sheincorp.cn/api/v4">
            <small class="form-text">GitLab服务器的API基础地址</small>
          </div>



          <div class="form-group">
            <label for="gitlab-token">GitLab访问令牌</label>
            <div class="input-group">
              <input type="password" id="gitlab-token" class="form-control" 
                     placeholder="输入GitLab个人访问令牌">
              <button type="button" class="btn btn-secondary" id="toggle-gitlab-token">
                <span class="btn-icon">👁️</span>
              </button>
            </div>
            <small class="form-text">
              在GitLab中：Preferences → Access Tokens → 创建令牌
              <a href="https://gitlab.sheincorp.cn/-/profile/personal_access_tokens" target="_blank">前往设置</a>
            </small>
          </div>
        </section>

        <!-- 数据提取设置 -->
        <section class="settings-section">
          <h2>数据提取设置</h2>
          
          <div class="form-group">
            <label for="max-results">最大提取数量</label>
            <input type="number" id="max-results" class="form-control" 
                   value="1000" min="1" max="10000">
            <small class="form-text">单次提取的最大问题数量</small>
          </div>

          <div class="form-group">
            <label>提取字段</label>
            <div class="checkbox-group">
              <label class="checkbox-label">
                <input type="checkbox" id="extract-key" checked>
                <span class="checkmark"></span>
                问题编号 (Key)
              </label>
              <label class="checkbox-label">
                <input type="checkbox" id="extract-summary" checked>
                <span class="checkmark"></span>
                标题 (Summary)
              </label>
              <label class="checkbox-label">
                <input type="checkbox" id="extract-status" checked>
                <span class="checkmark"></span>
                状态 (Status)
              </label>
              <label class="checkbox-label">
                <input type="checkbox" id="extract-assignee" checked>
                <span class="checkmark"></span>
                经办人 (Assignee)
              </label>
              <label class="checkbox-label">
                <input type="checkbox" id="extract-priority" checked>
                <span class="checkmark"></span>
                优先级 (Priority)
              </label>
              <label class="checkbox-label">
                <input type="checkbox" id="extract-description">
                <span class="checkmark"></span>
                描述 (Description)
              </label>
              <label class="checkbox-label">
                <input type="checkbox" id="extract-created">
                <span class="checkmark"></span>
                创建时间 (Created)
              </label>
              <label class="checkbox-label">
                <input type="checkbox" id="extract-updated">
                <span class="checkmark"></span>
                更新时间 (Updated)
              </label>
            </div>
          </div>

          <div class="form-group">
            <label for="auto-extract">自动提取</label>
            <label class="switch">
              <input type="checkbox" id="auto-extract">
              <span class="slider"></span>
            </label>
            <small class="form-text">在JIRA页面自动提取数据</small>
          </div>
        </section>

        <!-- 导出设置 -->
        <section class="settings-section">
          <h2>导出设置</h2>
          
          <div class="form-group">
            <label for="default-format">默认导出格式</label>
            <select id="default-format" class="form-control">
              <option value="csv">CSV</option>
              <option value="json">JSON</option>
              <option value="excel">Excel</option>
            </select>
          </div>

          <div class="form-group">
            <label for="filename-template">文件名模板</label>
            <input type="text" id="filename-template" class="form-control" 
                   value="jira-data-{date}" 
                   placeholder="jira-data-{date}">
            <small class="form-text">
              可用变量：{date}, {time}, {filter}, {count}
            </small>
          </div>

          <div class="form-group">
            <label for="csv-delimiter">CSV分隔符</label>
            <select id="csv-delimiter" class="form-control">
              <option value=",">逗号 (,)</option>
              <option value=";">分号 (;)</option>
              <option value="\t">制表符 (Tab)</option>
            </select>
          </div>
        </section>

        <!-- 通知设置 -->
        <section class="settings-section">
          <h2>通知设置</h2>
          
          <div class="form-group">
            <label for="show-notifications">显示通知</label>
            <label class="switch">
              <input type="checkbox" id="show-notifications" checked>
              <span class="slider"></span>
            </label>
            <small class="form-text">显示操作成功/失败的通知</small>
          </div>

          <div class="form-group">
            <label for="notification-duration">通知持续时间</label>
            <input type="range" id="notification-duration" class="form-range" 
                   min="1" max="10" value="3">
            <div class="range-labels">
              <span>1秒</span>
              <span id="duration-value">3秒</span>
              <span>10秒</span>
            </div>
          </div>
        </section>
      </div>

      <div class="actions">
        <button type="button" class="btn btn-primary" id="save-all">
          <span class="btn-icon">💾</span>
          保存所有设置
        </button>
        <button type="button" class="btn btn-secondary" id="reset-all">
          <span class="btn-icon">🔄</span>
          重置为默认值
        </button>
        <button type="button" class="btn btn-success" id="test-all">
          <span class="btn-icon">🧪</span>
          测试所有连接
        </button>
      </div>

      <div class="test-results" id="test-results" style="display: none;">
        <h3>连接测试结果</h3>
        <div class="test-item">
          <span class="test-label">GitLab连接:</span>
          <span class="test-status" id="gitlab-test-status">未测试</span>
        </div>
      </div>
    </main>

    <footer class="footer">
      <div class="footer-info">
        <p>DutyRobot Browser Extension v1.0.0</p>
        <p>基于原始dutyRobot项目改造的浏览器扩展版本</p>
      </div>
      <div class="footer-links">
        <a href="#" id="export-settings">导出设置</a>
        <a href="#" id="import-settings">导入设置</a>
        <a href="#" id="view-logs">查看日志</a>
      </div>
    </footer>
  </div>

  <script src="options.js"></script>
</body>
</html>