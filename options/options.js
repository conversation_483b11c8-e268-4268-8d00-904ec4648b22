// DutyRobot Extension Options Page Script

// 项目配置映射
const PROJECT_CONFIGS = {
  cn: {
    "wms": 424,
    "mot": 511,
    "wsk": 5730,
    "wacs": 10334,
    "wop": 19118,
    "ips": 2456,
    "ips-w": 2457,
    "wms-outbound": 15126,
    "wms-inbound": 15274,
    "mot-outbound": 17338,
    "mot-inbound": 17339,
    "wms-std": 26155,
    "mot-std": 26236,
    "wms-na": 15815,
    "mot-na": 16953,
    "wms-eu": 14695,
    "mot-eu": 14696,
    "wms-la": 12104,
    "mot-la": 12105,
    "wms-me": 21419,
    "mot-me": 21420,
    "lpmp": 9812,
    "lpmpm": 10100
  },
  wi: {
    "wms": 424,
    "mot": 511,
    "wsk": 5730,
    "wop": 19118,
    "ips": 2456,
    "ips-w": 2457,
    "wms-std": 26155,
    "mot-std": 26236,
    "lpmp": 9812,
    "lpmpm": 10100
  },
  overseas: {
    "wms-na": 15815,
    "mot-na": 16953,
    "wms-eu": 14695,
    "mot-eu": 14696,
    "wms-la": 12104,
    "mot-la": 12105,
    "wms-me": 21419,
    "mot-me": 21420
  }
};

// 默认设置
const DEFAULT_SETTINGS = {
  region: 'cn',
  gitlabApiUrl: 'https://gitlab.sheincorp.cn/api/v4',
  gitlabAccessToken: '',
  maxResults: 1000,
  extractFields: {
    key: true,
    summary: true,
    status: true,
    assignee: true,
    priority: true,
    description: false,
    created: false,
    updated: false
  },
  autoExtract: false,
  defaultFormat: 'csv',
  filenameTemplate: 'jira-data-{date}',
  csvDelimiter: ',',
  showNotifications: true,
  notificationDuration: 3,
  projectMapping: {},
  defaultProjects: []
};

// DOM元素
let elements = {};

// 初始化
document.addEventListener('DOMContentLoaded', async () => {
  initializeElements();
  await loadSettings();
  bindEvents();
  // 项目配置UI已隐藏，不需要更新显示
  // updateDefaultProjects();
});

// 初始化DOM元素引用
function initializeElements() {
  elements = {
    region: document.getElementById('region'),
    gitlabApiUrl: document.getElementById('gitlab-api-url'),
    gitlabToken: document.getElementById('gitlab-token'),
    toggleGitlabToken: document.getElementById('toggle-gitlab-token'),
    projectMapping: document.getElementById('project-mapping'),
    addProject: document.getElementById('add-project'),
    defaultProjects: document.getElementById('default-projects'),
    maxResults: document.getElementById('max-results'),
    extractKey: document.getElementById('extract-key'),
    extractSummary: document.getElementById('extract-summary'),
    extractStatus: document.getElementById('extract-status'),
    extractAssignee: document.getElementById('extract-assignee'),
    extractPriority: document.getElementById('extract-priority'),
    extractDescription: document.getElementById('extract-description'),
    extractCreated: document.getElementById('extract-created'),
    extractUpdated: document.getElementById('extract-updated'),
    autoExtract: document.getElementById('auto-extract'),
    defaultFormat: document.getElementById('default-format'),
    filenameTemplate: document.getElementById('filename-template'),
    csvDelimiter: document.getElementById('csv-delimiter'),
    showNotifications: document.getElementById('show-notifications'),
    notificationDuration: document.getElementById('notification-duration'),
    durationValue: document.getElementById('duration-value'),
    saveAll: document.getElementById('save-all'),
    resetAll: document.getElementById('reset-all'),
    testAll: document.getElementById('test-all'),
    testResults: document.getElementById('test-results'),
    gitlabTestStatus: document.getElementById('gitlab-test-status')
  };
}

// 加载设置
async function loadSettings() {
  try {
    const settings = await chrome.storage.sync.get(Object.keys(DEFAULT_SETTINGS));
    const mergedSettings = { ...DEFAULT_SETTINGS, ...settings };
    
    // 基本设置
    elements.region.value = mergedSettings.region;
    elements.gitlabApiUrl.value = mergedSettings.gitlabApiUrl;
    elements.gitlabToken.value = mergedSettings.gitlabAccessToken;
    
    // 数据提取设置
    elements.maxResults.value = mergedSettings.maxResults;
    elements.extractKey.checked = mergedSettings.extractFields.key;
    elements.extractSummary.checked = mergedSettings.extractFields.summary;
    elements.extractStatus.checked = mergedSettings.extractFields.status;
    elements.extractAssignee.checked = mergedSettings.extractFields.assignee;
    elements.extractPriority.checked = mergedSettings.extractFields.priority;
    elements.extractDescription.checked = mergedSettings.extractFields.description;
    elements.extractCreated.checked = mergedSettings.extractFields.created;
    elements.extractUpdated.checked = mergedSettings.extractFields.updated;
    elements.autoExtract.checked = mergedSettings.autoExtract;
    
    // 导出设置
    elements.defaultFormat.value = mergedSettings.defaultFormat;
    elements.filenameTemplate.value = mergedSettings.filenameTemplate;
    elements.csvDelimiter.value = mergedSettings.csvDelimiter;
    
    // 通知设置
    elements.showNotifications.checked = mergedSettings.showNotifications;
    elements.notificationDuration.value = mergedSettings.notificationDuration;
    elements.durationValue.textContent = `${mergedSettings.notificationDuration}秒`;
    
    // 项目配置 - 始终使用默认配置
    const defaultProjectMapping = PROJECT_CONFIGS[mergedSettings.region] || {};
    
    // 更新项目映射显示（隐藏状态下不需要显示）
    // updateProjectMappingDisplay(defaultProjectMapping);
    // updateDefaultProjectsDisplay(defaultProjectMapping, mergedSettings.defaultProjects);
    
  } catch (error) {
    console.error('加载设置失败:', error);
    showMessage('加载设置失败', 'error');
  }
}

// 绑定事件
function bindEvents() {
  // 密码显示切换
  elements.toggleGitlabToken.addEventListener('click', () => togglePasswordVisibility('gitlab-token'));
  
  // 地区变更
  elements.region.addEventListener('change', onRegionChange);
  
  // 项目管理 - 注释掉因为UI已隐藏
  // elements.addProject.addEventListener('click', addProjectMapping);
  
  // 通知持续时间滑块
  elements.notificationDuration.addEventListener('input', (e) => {
    elements.durationValue.textContent = `${e.target.value}秒`;
  });
  
  // 主要操作按钮
  elements.saveAll.addEventListener('click', saveAllSettings);
  elements.resetAll.addEventListener('click', resetAllSettings);
  elements.testAll.addEventListener('click', testAllConnections);
  
  // 导入导出设置
  document.getElementById('export-settings').addEventListener('click', exportSettings);
  document.getElementById('import-settings').addEventListener('click', importSettings);
  document.getElementById('view-logs').addEventListener('click', viewLogs);
}

// 切换密码可见性
function togglePasswordVisibility(inputId) {
  const input = document.getElementById(inputId);
  const button = input.nextElementSibling;
  
  if (input.type === 'password') {
    input.type = 'text';
    button.innerHTML = '<span class="btn-icon">🙈</span>';
  } else {
    input.type = 'password';
    button.innerHTML = '<span class="btn-icon">👁️</span>';
  }
}

// 地区变更处理
function onRegionChange() {
  const region = elements.region.value;
  const projectMapping = PROJECT_CONFIGS[region] || {};
  
  // 项目配置UI已隐藏，不需要更新显示
  // updateProjectMappingDisplay(projectMapping);
  // updateDefaultProjectsDisplay(projectMapping, []);
}

// 更新项目映射显示
function updateProjectMappingDisplay(mapping) {
  elements.projectMapping.innerHTML = '';
  
  Object.entries(mapping).forEach(([name, id]) => {
    addProjectMappingItem(name, id);
  });
}

// 添加项目映射项
function addProjectMappingItem(name = '', id = '') {
  const item = document.createElement('div');
  item.className = 'project-item';
  
  item.innerHTML = `
    <input type="text" placeholder="项目名称" value="${name}" class="project-name">
    <input type="number" placeholder="项目ID" value="${id}" class="project-id">
    <button type="button" class="btn btn-secondary btn-small remove-project">
      <span class="btn-icon">🗑️</span>
    </button>
  `;
  
  // 绑定删除事件
  item.querySelector('.remove-project').addEventListener('click', () => {
    item.remove();
    updateDefaultProjects();
  });
  
  // 绑定名称变更事件
  item.querySelector('.project-name').addEventListener('input', updateDefaultProjects);
  
  elements.projectMapping.appendChild(item);
}

// 添加项目映射
function addProjectMapping() {
  addProjectMappingItem();
  updateDefaultProjects();
}

// 更新默认项目显示
function updateDefaultProjectsDisplay(mapping, selected = []) {
  elements.defaultProjects.innerHTML = '';
  
  Object.keys(mapping).forEach(projectName => {
    const label = document.createElement('label');
    label.className = 'checkbox-label';
    
    const checkbox = document.createElement('input');
    checkbox.type = 'checkbox';
    checkbox.value = projectName;
    checkbox.checked = selected.includes(projectName);
    
    label.appendChild(checkbox);
    label.appendChild(document.createTextNode(projectName));
    
    elements.defaultProjects.appendChild(label);
  });
}

// 更新默认项目
function updateDefaultProjects() {
  const projectItems = elements.projectMapping.querySelectorAll('.project-item');
  const mapping = {};
  
  projectItems.forEach(item => {
    const name = item.querySelector('.project-name').value.trim();
    const id = item.querySelector('.project-id').value.trim();
    if (name && id) {
      mapping[name] = parseInt(id);
    }
  });
  
  const currentSelected = Array.from(elements.defaultProjects.querySelectorAll('input:checked'))
    .map(cb => cb.value);
  
  updateDefaultProjectsDisplay(mapping, currentSelected);
}

// 保存所有设置
async function saveAllSettings() {
  try {
    elements.saveAll.disabled = true;
    elements.saveAll.innerHTML = '<span class="btn-icon">⏳</span>保存中...';
    
    // 使用默认项目配置而不是从UI收集
    const region = elements.region.value;
    const projectMapping = PROJECT_CONFIGS[region] || {};
    
    // 默认项目为空数组，使用所有可用项目
    const defaultProjects = Object.keys(projectMapping);
    
    // 收集提取字段设置
    const extractFields = {
      key: elements.extractKey.checked,
      summary: elements.extractSummary.checked,
      status: elements.extractStatus.checked,
      assignee: elements.extractAssignee.checked,
      priority: elements.extractPriority.checked,
      description: elements.extractDescription.checked,
      created: elements.extractCreated.checked,
      updated: elements.extractUpdated.checked
    };
    
    // 构建设置对象
    const settings = {
      region: elements.region.value,
      gitlabApiUrl: elements.gitlabApiUrl.value,
      gitlabAccessToken: elements.gitlabToken.value,
      projectMapping,
      defaultProjects,
      maxResults: parseInt(elements.maxResults.value),
      extractFields,
      autoExtract: elements.autoExtract.checked,
      defaultFormat: elements.defaultFormat.value,
      filenameTemplate: elements.filenameTemplate.value,
      csvDelimiter: elements.csvDelimiter.value,
      showNotifications: elements.showNotifications.checked,
      notificationDuration: parseInt(elements.notificationDuration.value)
    };
    
    await chrome.storage.sync.set(settings);
    showMessage('所有设置已保存', 'success');
    
  } catch (error) {
    console.error('保存设置失败:', error);
    showMessage('保存设置失败: ' + error.message, 'error');
  } finally {
    elements.saveAll.disabled = false;
    elements.saveAll.innerHTML = '<span class="btn-icon">💾</span>保存所有设置';
  }
}

// 重置所有设置
async function resetAllSettings() {
  if (!confirm('确定要重置所有设置为默认值吗？此操作不可撤销。')) {
    return;
  }
  
  try {
    await chrome.storage.sync.clear();
    await chrome.storage.sync.set(DEFAULT_SETTINGS);
    
    // 重新加载页面以显示默认设置
    location.reload();
    
  } catch (error) {
    console.error('重置设置失败:', error);
    showMessage('重置设置失败: ' + error.message, 'error');
  }
}

// 测试所有连接
async function testAllConnections() {
  elements.testAll.disabled = true;
  elements.testAll.innerHTML = '<span class="btn-icon">⏳</span>测试中...';
  elements.testResults.style.display = 'block';
  
  // 重置状态
  elements.gitlabTestStatus.textContent = '测试中...';
  elements.gitlabTestStatus.className = 'test-status testing';
  
  // 测试GitLab连接
  try {
    const gitlabResponse = await chrome.runtime.sendMessage({
      action: 'fetchGitlabData',
      projectId: '424', // 测试项目ID
      endpoint: ''
    });
    
    if (gitlabResponse && gitlabResponse.success) {
      elements.gitlabTestStatus.textContent = '连接成功';
      elements.gitlabTestStatus.className = 'test-status success';
    } else {
      elements.gitlabTestStatus.textContent = '连接失败';
      elements.gitlabTestStatus.className = 'test-status error';
    }
  } catch (error) {
    elements.gitlabTestStatus.textContent = '连接失败';
    elements.gitlabTestStatus.className = 'test-status error';
  }
  
  elements.testAll.disabled = false;
  elements.testAll.innerHTML = '<span class="btn-icon">🧪</span>测试所有连接';
}

// 导出设置
async function exportSettings() {
  try {
    const settings = await chrome.storage.sync.get();
    const dataStr = JSON.stringify(settings, null, 2);
    const dataBlob = new Blob([dataStr], { type: 'application/json' });
    
    const link = document.createElement('a');
    link.href = URL.createObjectURL(dataBlob);
    link.download = `dutyrobot-settings-${new Date().toISOString().split('T')[0]}.json`;
    link.click();
    
    showMessage('设置已导出', 'success');
  } catch (error) {
    console.error('导出设置失败:', error);
    showMessage('导出设置失败', 'error');
  }
}

// 导入设置
function importSettings() {
  const input = document.createElement('input');
  input.type = 'file';
  input.accept = '.json';
  
  input.onchange = async (e) => {
    const file = e.target.files[0];
    if (!file) return;
    
    try {
      const text = await file.text();
      const settings = JSON.parse(text);
      
      await chrome.storage.sync.set(settings);
      showMessage('设置已导入，页面将刷新', 'success');
      
      setTimeout(() => {
        location.reload();
      }, 1500);
      
    } catch (error) {
      console.error('导入设置失败:', error);
      showMessage('导入设置失败: ' + error.message, 'error');
    }
  };
  
  input.click();
}

// 查看日志
function viewLogs() {
  chrome.tabs.create({ url: 'chrome://extensions/' });
}

// 显示消息
function showMessage(message, type = 'info') {
  const messageDiv = document.createElement('div');
  messageDiv.style.cssText = `
    position: fixed;
    top: 20px;
    right: 20px;
    padding: 12px 20px;
    border-radius: 6px;
    color: white;
    font-weight: 500;
    z-index: 10000;
    max-width: 300px;
    box-shadow: 0 4px 12px rgba(0,0,0,0.2);
    transition: all 0.3s ease;
  `;
  
  switch (type) {
    case 'success':
      messageDiv.style.backgroundColor = '#28a745';
      break;
    case 'warning':
      messageDiv.style.backgroundColor = '#ffc107';
      messageDiv.style.color = '#212529';
      break;
    case 'error':
      messageDiv.style.backgroundColor = '#dc3545';
      break;
    default:
      messageDiv.style.backgroundColor = '#17a2b8';
  }
  
  messageDiv.textContent = message;
  document.body.appendChild(messageDiv);
  
  setTimeout(() => {
    if (messageDiv.parentNode) {
      messageDiv.style.opacity = '0';
      setTimeout(() => {
        messageDiv.parentNode.removeChild(messageDiv);
      }, 300);
    }
  }, 3000);
}