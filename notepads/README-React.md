# DutyRobot Chrome Extension - React Version

这是DutyRobot Chrome扩展的React版本，提供了现代化的用户界面和更好的开发体验。

## 项目结构

```
src/
├── popup/                 # 弹出窗口组件
│   ├── index.jsx         # 入口文件
│   ├── PopupApp.jsx      # 主应用组件
│   └── popup.css         # 样式文件
├── options/              # 选项页面组件
│   ├── index.jsx         # 入口文件
│   ├── OptionsApp.jsx    # 选项页面组件
│   └── options.css       # 样式文件
├── components/           # 共享组件
│   ├── TabNavigation.jsx # 标签导航
│   ├── ExtractTab.jsx    # 数据提取标签
│   ├── ToolsTab.jsx      # 工具箱标签
│   └── SettingsTab.jsx   # 设置标签
├── background/           # 后台脚本
│   └── background.js     # 后台服务
├── content-scripts/      # 内容脚本
│   └── jira-content.js   # JIRA页面内容脚本
└── utils/                # 工具函数
    └── index.js          # 通用工具函数
```

## 功能特性

### 数据提取
- 支持从JIRA页面提取问题数据
- 支持单个问题和问题列表页面
- 可配置提取字段
- 支持CSV和JSON格式导出

### GitLab集成
- 连接GitLab API
- 获取项目列表
- 项目配置管理

### 设置管理
- 区域设置
- GitLab API配置
- 提取字段配置
- 通知设置

## 开发指南

### 环境要求
- Node.js 16+
- npm 或 yarn

### 安装依赖
```bash
npm install
```

### 开发构建
```bash
npm run build
```

### 生产构建
```bash
npm run build:prod
```

### 开发模式
```bash
npm run dev
```

## 安装扩展

1. 构建项目：
   ```bash
   npm run build
   ```

2. 打开Chrome浏览器，进入扩展管理页面：
   - 地址栏输入：`chrome://extensions/`
   - 或者：菜单 → 更多工具 → 扩展程序

3. 开启开发者模式（右上角开关）

4. 点击"加载已解压的扩展程序"

5. 选择项目的 `dist` 目录

6. 扩展安装完成，可以在工具栏看到DutyRobot图标

## 使用说明

### 基本使用
1. 访问JIRA页面
2. 点击扩展图标打开弹出窗口
3. 在"数据提取"标签中提取数据
4. 选择导出格式（CSV/JSON）
5. 复制或下载数据

### GitLab配置
1. 点击扩展图标，切换到"设置"标签
2. 或者右键扩展图标，选择"选项"
3. 配置GitLab API URL和访问令牌
4. 测试连接
5. 保存设置

### 高级设置
- 自定义提取字段
- 设置最大提取数量
- 配置默认导出格式
- 开启/关闭通知

## 技术栈

- **前端框架**: React 18
- **构建工具**: Webpack 5
- **编译器**: Babel
- **样式**: CSS3
- **API**: Chrome Extension APIs

## 开发特性

- 现代化React架构
- 组件化开发
- 热重载支持（开发模式）
- TypeScript支持（可选）
- ESLint代码检查
- 模块化CSS

## 文件说明

### 配置文件
- `webpack.config.js` - Webpack构建配置
- `.babelrc` - Babel编译配置
- `package.json` - 项目依赖和脚本
- `public/manifest.json` - Chrome扩展清单

### 构建输出
- `dist/` - 构建输出目录
- `dist/manifest.json` - 扩展清单文件
- `dist/popup.html` - 弹出窗口HTML
- `dist/options.html` - 选项页面HTML
- `dist/*.js` - 编译后的JavaScript文件

## 故障排除

### 常见问题

1. **构建失败**
   - 检查Node.js版本（需要16+）
   - 删除`node_modules`重新安装依赖
   - 检查网络连接

2. **扩展无法加载**
   - 确保已构建项目（运行`npm run build`）
   - 检查`dist`目录是否存在
   - 查看Chrome扩展管理页面的错误信息

3. **JIRA页面无法提取数据**
   - 确保在JIRA页面上使用
   - 检查页面是否完全加载
   - 查看浏览器控制台错误信息

4. **GitLab连接失败**
   - 检查API URL格式（应包含协议，如https://）
   - 验证访问令牌权限
   - 确保网络可以访问GitLab服务器

### 调试技巧

1. **查看扩展日志**
   - 打开Chrome开发者工具
   - 切换到Console标签
   - 查看相关错误信息

2. **调试后台脚本**
   - 进入`chrome://extensions/`
   - 找到DutyRobot扩展
   - 点击"检查视图"中的"背景页"

3. **调试内容脚本**
   - 在JIRA页面打开开发者工具
   - 查看Console中的内容脚本日志

## 更新日志

### v1.0.0 (React版本)
- 迁移到React架构
- 现代化UI设计
- 改进的组件结构
- 更好的错误处理
- 优化的构建流程

## 贡献指南

1. Fork项目
2. 创建功能分支
3. 提交更改
4. 推送到分支
5. 创建Pull Request

## 许可证

本项目采用MIT许可证。详见LICENSE文件。

## 支持

如有问题或建议，请创建Issue或联系开发团队。