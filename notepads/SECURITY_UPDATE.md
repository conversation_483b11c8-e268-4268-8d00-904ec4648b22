# 安全更新说明

## 更新内容

由于公司安全政策要求，已移除所有 JIRA API 相关配置和功能。现在扩展改为直接从页面获取数据。

## 主要变更

### 1. 移除的功能
- JIRA API 地址配置
- JIRA 访问令牌配置
- JIRA API 连接测试
- 基于 JIRA API 的数据获取功能

### 2. 保留的功能
- 页面数据提取功能（通过 content script）
- GitLab API 相关功能
- 数据导出功能（CSV、JSON）
- 项目配置管理

### 3. 修改的文件
- `options/options.js` - 移除 JIRA API 相关配置
- `options/options.html` - 移除 JIRA API 相关 UI 元素
- `background.js` - 移除 JIRA API 请求处理
- `popup/popup.js` - 修改数据提取方式，移除 API 调用
- `popup/popup.html` - 移除 JIRA 令牌配置 UI
- `manifest.json` - 移除 JIRA API 权限

## 使用说明

### 数据提取方式变更

**之前：** 通过 JIRA API 获取数据
- 需要配置 JIRA 访问令牌
- 可以获取大量数据
- 支持复杂查询

**现在：** 直接从页面获取数据
- 无需配置 JIRA 访问令牌
- 只能获取当前页面显示的数据
- 依赖页面结构

### 如何使用

1. 在 JIRA 页面打开扩展
2. 点击「提取当前页面数据」或「提取筛选器数据」
3. 扩展会自动从页面 DOM 中提取数据
4. 可以导出为 CSV 或 JSON 格式

### 注意事项

- 确保 JIRA 页面完全加载后再进行数据提取
- 如果页面结构发生变化，可能需要更新 content script
- 数据提取量受页面显示限制

## 安全性提升

- 不再需要存储和传输 JIRA 访问令牌
- 减少了网络请求和数据传输
- 降低了敏感信息泄露风险