// Background script for DutyRobot Extension - Service Worker

// 扩展安装时的初始化
chrome.runtime.onInstalled.addListener(() => {
  console.log('DutyRobot Extension installed');
  
  // 设置默认配置
  chrome.storage.sync.set({
    region: 'cn',
    gitlabApiUrl: 'https://gitlab.sheincorp.cn/api/v4',
    showNotifications: true
  });
});

// 处理扩展启动
chrome.runtime.onStartup.addListener(() => {
  console.log('DutyRobot Extension started');
});

// 错误处理
self.addEventListener('unhandledrejection', (event) => {
  console.error('Unhandled promise rejection in background script:', event.reason);
});

// 监听来自content script的消息
chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
  if (request.action === 'fetchGitlabData') {
    fetchGitlabData(request.projectId, request.endpoint)
      .then(data => sendResponse({ success: true, data }))
      .catch(error => sendResponse({ success: false, error: error.message }));
    return true;
  }
});



// 获取GitLab数据
async function fetchGitlabData(projectId, endpoint) {
  try {
    const { gitlabAccessToken, gitlabApiUrl } = await chrome.storage.sync.get(['gitlabAccessToken', 'gitlabApiUrl']);
    
    if (!gitlabAccessToken) {
      throw new Error('GitLab访问令牌未配置，请在选项页面中设置');
    }
    
    const url = `${gitlabApiUrl}/projects/${projectId}/${endpoint}`;
    
    const response = await fetch(url, {
      headers: {
        'Private-Token': gitlabAccessToken,
        'Content-Type': 'application/json'
      }
    });
    
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }
    
    return await response.json();
  } catch (error) {
    console.error('Error fetching GitLab data:', error);
    throw error;
  }
}

// 创建右键菜单
chrome.runtime.onInstalled.addListener(() => {
  chrome.contextMenus.create({
    id: 'dutyRobotExtract',
    title: '提取JIRA数据',
    contexts: ['page'],
    documentUrlPatterns: ['https://jira.dotfashion.cn/*']
  });
});

// 处理右键菜单点击
chrome.contextMenus.onClicked.addListener((info, tab) => {
  if (info.menuItemId === 'dutyRobotExtract') {
    chrome.tabs.sendMessage(tab.id, { action: 'extractJiraData' }, (response) => {
      if (chrome.runtime.lastError) {
        console.error('Error sending message to content script:', chrome.runtime.lastError);
        showNotification('错误', '无法从页面提取数据');
      } else if (response && response.success) {
        const count = response.data ? response.data.length : 0;
        showNotification('成功', `已提取 ${count} 个JIRA问题`);
      } else {
        showNotification('错误', response?.error || '提取数据失败');
      }
    });
  }
});

// 显示通知
function showNotification(title, message) {
  chrome.storage.sync.get(['showNotifications'], (result) => {
    if (result.showNotifications !== false) {
      chrome.notifications.create({
        type: 'basic',
        iconUrl: 'icons/icon48.svg',
        title: `DutyRobot - ${title}`,
        message: message
      });
    }
  });
}